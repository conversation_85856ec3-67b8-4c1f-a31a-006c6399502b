#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境健康检查脚本
用于监控系统状态和数据同步健康度
"""

import os
import sys
import subprocess
import json
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import config
from utils import get_logger

logger = get_logger(__name__)


class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self.checks = []
        self.warnings = []
        self.errors = []
    
    def check_config(self) -> bool:
        """检查配置"""
        try:
            is_valid = config.validate()
            if is_valid:
                self.checks.append("✅ 配置验证通过")
                return True
            else:
                self.errors.append("❌ 配置验证失败")
                return False
        except Exception as e:
            self.errors.append(f"❌ 配置检查异常: {e}")
            return False
    
    def check_api_connection(self) -> bool:
        """检查API连接"""
        try:
            from services import ZsxqCrawler
            crawler = ZsxqCrawler()
            
            # 尝试获取1条数据测试连接
            data = crawler._make_request({"scope": "all", "count": 1})
            if data:
                self.checks.append("✅ 知识星球API连接正常")
                return True
            else:
                self.errors.append("❌ 知识星球API连接失败")
                return False
        except Exception as e:
            self.errors.append(f"❌ API连接检查异常: {e}")
            return False
    
    def check_database_connection(self) -> bool:
        """检查数据库连接"""
        try:
            from services import StorageService
            storage = StorageService()
            
            # 尝试查询一条记录测试连接
            result = storage.client.table('users').select('user_id').limit(1).execute()
            if result:
                self.checks.append("✅ Supabase数据库连接正常")
                return True
            else:
                self.errors.append("❌ Supabase数据库连接失败")
                return False
        except Exception as e:
            self.errors.append(f"❌ 数据库连接检查异常: {e}")
            return False
    
    def check_disk_space(self) -> bool:
        """检查磁盘空间"""
        try:
            data_dir = Path(config.app.data_dir)
            if not data_dir.exists():
                data_dir.mkdir(parents=True, exist_ok=True)
            
            # 检查可用空间
            stat = os.statvfs(data_dir)
            free_bytes = stat.f_bavail * stat.f_frsize
            free_gb = free_bytes / (1024**3)
            
            if free_gb > 5:  # 大于5GB
                self.checks.append(f"✅ 磁盘空间充足 ({free_gb:.1f}GB可用)")
                return True
            elif free_gb > 1:  # 1-5GB
                self.warnings.append(f"⚠️ 磁盘空间不足 ({free_gb:.1f}GB可用)")
                return True
            else:  # 小于1GB
                self.errors.append(f"❌ 磁盘空间严重不足 ({free_gb:.1f}GB可用)")
                return False
        except Exception as e:
            self.errors.append(f"❌ 磁盘空间检查异常: {e}")
            return False
    
    def check_data_files(self) -> bool:
        """检查数据文件"""
        try:
            from workflows import DownloadWorkflow
            workflow = DownloadWorkflow()
            files = workflow.list_data_files()
            
            if files:
                latest_file = files[0]
                file_path = Path(config.app.data_dir) / latest_file
                
                # 检查最新文件的修改时间
                mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                age = datetime.now() - mtime
                
                if age.days == 0:
                    self.checks.append(f"✅ 数据文件正常 (最新: {latest_file})")
                elif age.days <= 1:
                    self.warnings.append(f"⚠️ 数据文件较旧 (最新: {latest_file}, {age.days}天前)")
                else:
                    self.errors.append(f"❌ 数据文件过旧 (最新: {latest_file}, {age.days}天前)")
                
                return True
            else:
                self.warnings.append("⚠️ 没有找到数据文件")
                return True
        except Exception as e:
            self.errors.append(f"❌ 数据文件检查异常: {e}")
            return False
    
    def check_process_state(self) -> bool:
        """检查处理状态"""
        try:
            from services import StateManager
            state_manager = StateManager()
            state = state_manager.load_progress()
            
            user_count = len(state.processed_user_ids)
            topic_count = len(state.processed_topic_ids)
            
            if user_count > 0 or topic_count > 0:
                self.checks.append(f"✅ 处理状态正常 (用户: {user_count}, 帖子: {topic_count})")
                
                # 检查最后更新时间
                if state.last_update:
                    last_update = datetime.fromisoformat(state.last_update.replace('Z', '+00:00'))
                    age = datetime.now() - last_update.replace(tzinfo=None)
                    
                    if age.days > 7:
                        self.warnings.append(f"⚠️ 处理状态较旧 (最后更新: {age.days}天前)")
                
                return True
            else:
                self.warnings.append("⚠️ 处理状态为空")
                return True
        except Exception as e:
            self.errors.append(f"❌ 处理状态检查异常: {e}")
            return False
    
    def check_service_status(self) -> bool:
        """检查系统服务状态"""
        try:
            # 检查 systemd 服务状态（如果存在）
            result = subprocess.run(['systemctl', 'is-active', 'zsxq-sync'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                status = result.stdout.strip()
                if status == 'active':
                    self.checks.append("✅ 系统服务运行正常")
                else:
                    self.warnings.append(f"⚠️ 系统服务状态: {status}")
            else:
                self.warnings.append("⚠️ 系统服务未配置或不存在")
            
            return True
        except Exception as e:
            self.warnings.append(f"⚠️ 无法检查系统服务状态: {e}")
            return True
    
    def run_all_checks(self) -> dict:
        """运行所有健康检查"""
        print("=" * 60)
        print("知识星球数据同步系统 - 健康检查")
        print("=" * 60)
        
        checks = [
            ("配置检查", self.check_config),
            ("API连接检查", self.check_api_connection),
            ("数据库连接检查", self.check_database_connection),
            ("磁盘空间检查", self.check_disk_space),
            ("数据文件检查", self.check_data_files),
            ("处理状态检查", self.check_process_state),
            ("服务状态检查", self.check_service_status),
        ]
        
        results = {}
        for name, check_func in checks:
            print(f"\n{name}...")
            try:
                results[name] = check_func()
            except Exception as e:
                self.errors.append(f"❌ {name}异常: {e}")
                results[name] = False
        
        # 输出结果
        print("\n" + "=" * 60)
        print("检查结果汇总")
        print("=" * 60)
        
        if self.checks:
            print("\n✅ 正常项目:")
            for check in self.checks:
                print(f"  {check}")
        
        if self.warnings:
            print("\n⚠️ 警告项目:")
            for warning in self.warnings:
                print(f"  {warning}")
        
        if self.errors:
            print("\n❌ 错误项目:")
            for error in self.errors:
                print(f"  {error}")
        
        # 总体状态
        print("\n" + "=" * 60)
        if self.errors:
            print("❌ 系统状态: 异常")
            overall_status = "ERROR"
        elif self.warnings:
            print("⚠️ 系统状态: 警告")
            overall_status = "WARNING"
        else:
            print("✅ 系统状态: 正常")
            overall_status = "OK"
        
        print("=" * 60)
        
        return {
            "status": overall_status,
            "checks": len(self.checks),
            "warnings": len(self.warnings),
            "errors": len(self.errors),
            "details": {
                "checks": self.checks,
                "warnings": self.warnings,
                "errors": self.errors
            },
            "results": results,
            "timestamp": datetime.now().isoformat()
        }


def main():
    """主函数"""
    checker = HealthChecker()
    result = checker.run_all_checks()
    
    # 保存检查结果到文件
    result_file = Path("health_check_result.json")
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n检查结果已保存到: {result_file}")
    
    # 根据结果设置退出码
    if result["status"] == "ERROR":
        sys.exit(1)
    elif result["status"] == "WARNING":
        sys.exit(2)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
