#!/bin/bash
# -*- coding: utf-8 -*-
"""
生产环境部署脚本
用于自动化部署知识星球数据同步系统
"""

set -e  # 遇到错误立即退出

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
DEPLOY_USER="${DEPLOY_USER:-zsxq}"
DEPLOY_DIR="${DEPLOY_DIR:-/opt/zsxq-tool}"
PYTHON_VERSION="${PYTHON_VERSION:-3.9}"
SERVICE_NAME="zsxq-sync"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查运行权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要 root 权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统依赖
check_system_dependencies() {
    log_step "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查必需的命令
    local required_commands=("python3" "pip3" "git" "systemctl" "crontab")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("$cmd")
        fi
    done
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少以下依赖: ${missing_deps[*]}"
        log_info "请先安装缺少的依赖"
        exit 1
    fi
    
    log_info "系统依赖检查通过"
}

# 创建部署用户
create_deploy_user() {
    log_step "创建部署用户..."
    
    if id "$DEPLOY_USER" &>/dev/null; then
        log_info "用户 $DEPLOY_USER 已存在"
    else
        useradd -r -s /bin/bash -d "$DEPLOY_DIR" "$DEPLOY_USER"
        log_info "用户 $DEPLOY_USER 创建成功"
    fi
}

# 创建部署目录
create_deploy_directory() {
    log_step "创建部署目录..."
    
    mkdir -p "$DEPLOY_DIR"
    mkdir -p "$DEPLOY_DIR/data"
    mkdir -p "$DEPLOY_DIR/logs"
    mkdir -p "/backup/zsxq"
    
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$DEPLOY_DIR"
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "/backup/zsxq"
    
    log_info "部署目录创建完成: $DEPLOY_DIR"
}

# 复制项目文件
copy_project_files() {
    log_step "复制项目文件..."
    
    # 复制源代码
    rsync -av --exclude='.git' --exclude='__pycache__' --exclude='*.pyc' \
          --exclude='.env' --exclude='data/' --exclude='backup_old_code/' \
          "$PROJECT_DIR/" "$DEPLOY_DIR/"
    
    # 设置文件权限
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$DEPLOY_DIR"
    chmod +x "$DEPLOY_DIR/main.py"
    chmod +x "$DEPLOY_DIR/scripts/"*.sh
    
    log_info "项目文件复制完成"
}

# 创建 Python 虚拟环境
create_virtual_environment() {
    log_step "创建 Python 虚拟环境..."
    
    local venv_dir="$DEPLOY_DIR/venv"
    
    if [ -d "$venv_dir" ]; then
        log_info "虚拟环境已存在，删除旧环境"
        rm -rf "$venv_dir"
    fi
    
    # 创建虚拟环境
    sudo -u "$DEPLOY_USER" python3 -m venv "$venv_dir"
    
    # 升级 pip
    sudo -u "$DEPLOY_USER" "$venv_dir/bin/pip" install --upgrade pip
    
    # 安装依赖
    sudo -u "$DEPLOY_USER" "$venv_dir/bin/pip" install -r "$DEPLOY_DIR/requirements.txt"
    
    log_info "Python 虚拟环境创建完成"
}

# 创建配置文件
create_config_files() {
    log_step "创建配置文件..."
    
    # 创建 .env 文件模板
    if [ ! -f "$DEPLOY_DIR/.env" ]; then
        cp "$DEPLOY_DIR/.env.example" "$DEPLOY_DIR/.env"
        chown "$DEPLOY_USER:$DEPLOY_USER" "$DEPLOY_DIR/.env"
        chmod 600 "$DEPLOY_DIR/.env"
        
        log_warn "请编辑 $DEPLOY_DIR/.env 文件配置生产环境参数"
    else
        log_info ".env 文件已存在，跳过创建"
    fi
}

# 创建 systemd 服务
create_systemd_service() {
    log_step "创建 systemd 服务..."
    
    cat > "/etc/systemd/system/$SERVICE_NAME.service" << EOF
[Unit]
Description=知识星球数据同步服务
After=network.target

[Service]
Type=oneshot
User=$DEPLOY_USER
Group=$DEPLOY_USER
WorkingDirectory=$DEPLOY_DIR
Environment=PATH=$DEPLOY_DIR/venv/bin
ExecStart=/bin/bash -c '$DEPLOY_DIR/venv/bin/python $DEPLOY_DIR/main.py download && $DEPLOY_DIR/venv/bin/python $DEPLOY_DIR/main.py upload'
StandardOutput=journal
StandardError=journal
TimeoutStartSec=3600

[Install]
WantedBy=multi-user.target
EOF
    
    # 创建定时器
    cat > "/etc/systemd/system/$SERVICE_NAME.timer" << EOF
[Unit]
Description=知识星球数据同步定时器
Requires=$SERVICE_NAME.service

[Timer]
OnCalendar=daily
Persistent=true

[Install]
WantedBy=timers.target
EOF
    
    # 重新加载 systemd
    systemctl daemon-reload
    
    # 启用定时器
    systemctl enable "$SERVICE_NAME.timer"
    systemctl start "$SERVICE_NAME.timer"
    
    log_info "systemd 服务创建完成"
}

# 创建日志轮转配置
create_logrotate_config() {
    log_step "创建日志轮转配置..."
    
    cat > "/etc/logrotate.d/$SERVICE_NAME" << EOF
$DEPLOY_DIR/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $DEPLOY_USER $DEPLOY_USER
    postrotate
        systemctl reload-or-restart $SERVICE_NAME || true
    endscript
}
EOF
    
    log_info "日志轮转配置创建完成"
}

# 设置定时任务
setup_cron_jobs() {
    log_step "设置定时任务..."

    # 为部署用户创建 crontab
    local cron_file="/tmp/zsxq_crontab"

    cat > "$cron_file" << EOF
# 知识星球数据同步定时任务（使用新的sync命令）

# 每小时同步（过去1小时的新内容）- 包含数据清洗
0 * * * * cd $DEPLOY_DIR && $DEPLOY_DIR/venv/bin/python main.py sync -r hour --skip-users --skip-group >> $DEPLOY_DIR/logs/hourly.log 2>&1

# 每天凌晨2点同步（今天所有内容）- 包含数据清洗
0 2 * * * cd $DEPLOY_DIR && $DEPLOY_DIR/venv/bin/python main.py sync -r day --skip-group >> $DEPLOY_DIR/logs/daily.log 2>&1

# 每周日凌晨3点同步（过去一周所有内容）- 包含数据清洗
0 3 * * 0 cd $DEPLOY_DIR && $DEPLOY_DIR/venv/bin/python main.py sync -r week >> $DEPLOY_DIR/logs/weekly.log 2>&1

# 每月1号凌晨4点同步（过去一个月所有内容）- 包含数据清洗
0 4 1 * * cd $DEPLOY_DIR && $DEPLOY_DIR/venv/bin/python main.py sync -r month >> $DEPLOY_DIR/logs/monthly.log 2>&1

# 每天凌晨5点执行备份
0 5 * * * $DEPLOY_DIR/scripts/backup.sh >> $DEPLOY_DIR/logs/backup.log 2>&1

# 每2小时执行健康检查
0 */2 * * * $DEPLOY_DIR/venv/bin/python $DEPLOY_DIR/scripts/health_check.py >> $DEPLOY_DIR/logs/health.log 2>&1

# 日志清理任务（每天凌晨1点清理30天前的日志）
0 1 * * * find $DEPLOY_DIR/logs -name "*.log" -mtime +30 -delete

# 数据文件清理任务（每周清理旧的数据文件）
0 1 * * 1 cd $DEPLOY_DIR && find data/ -name "zsxq_topics_*.jsonl" -mtime +7 -delete
EOF

    # 安装 crontab
    sudo -u "$DEPLOY_USER" crontab "$cron_file"
    rm "$cron_file"

    log_info "定时任务设置完成"
    log_info "已配置以下定时任务："
    log_info "  - 每小时同步（跳过用户和星球处理）"
    log_info "  - 每日同步（跳过星球处理）"
    log_info "  - 每周同步（完整处理）"
    log_info "  - 每月同步（完整处理）"
    log_info "  - 每日备份和健康检查"
    log_info "  - 自动日志和数据清理"
}

# 创建管理脚本
create_management_scripts() {
    log_step "创建管理脚本..."
    
    # 创建启动脚本
    cat > "$DEPLOY_DIR/start.sh" << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
python main.py sync -r day
EOF

    # 创建状态检查脚本
    cat > "$DEPLOY_DIR/status.sh" << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
python main.py status
EOF
    
    # 创建健康检查脚本
    cat > "$DEPLOY_DIR/health.sh" << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
python scripts/health_check.py
EOF
    
    # 设置执行权限
    chmod +x "$DEPLOY_DIR"/*.sh
    chown "$DEPLOY_USER:$DEPLOY_USER" "$DEPLOY_DIR"/*.sh
    
    log_info "管理脚本创建完成"
}

# 运行初始化测试
run_initial_test() {
    log_step "运行初始化测试..."
    
    # 切换到部署用户运行测试
    sudo -u "$DEPLOY_USER" bash -c "
        cd $DEPLOY_DIR
        source venv/bin/activate
        python -c 'from config import config; print(\"配置加载:\", \"成功\" if config else \"失败\")'
        python scripts/health_check.py
    "
    
    log_info "初始化测试完成"
}

# 显示部署后信息
show_deployment_info() {
    log_step "部署完成信息"
    
    cat << EOF

🎉 知识星球数据同步系统部署完成！

📁 部署目录: $DEPLOY_DIR
👤 运行用户: $DEPLOY_USER
🔧 服务名称: $SERVICE_NAME

📋 下一步操作:
1. 编辑配置文件: sudo -u $DEPLOY_USER nano $DEPLOY_DIR/.env
2. 测试配置: sudo -u $DEPLOY_USER $DEPLOY_DIR/health.sh
3. 手动运行: sudo -u $DEPLOY_USER $DEPLOY_DIR/start.sh
4. 查看服务状态: systemctl status $SERVICE_NAME.timer
5. 查看日志: journalctl -u $SERVICE_NAME -f

🛠️ 管理命令:
- 启动同步: sudo -u $DEPLOY_USER $DEPLOY_DIR/start.sh
- 查看状态: sudo -u $DEPLOY_USER $DEPLOY_DIR/status.sh
- 健康检查: sudo -u $DEPLOY_USER $DEPLOY_DIR/health.sh
- 手动备份: sudo -u $DEPLOY_USER $DEPLOY_DIR/scripts/backup.sh

📊 监控:
- 服务状态: systemctl status $SERVICE_NAME.timer
- 同步日志: tail -f $DEPLOY_DIR/logs/sync.log
- 备份日志: tail -f $DEPLOY_DIR/logs/backup.log
- 健康日志: tail -f $DEPLOY_DIR/logs/health.log

⚠️  重要提醒:
请确保在 $DEPLOY_DIR/.env 文件中配置正确的生产环境参数！

EOF
}

# 主函数
main() {
    log_info "开始部署知识星球数据同步系统..."
    
    # 检查权限
    check_permissions
    
    # 检查系统依赖
    check_system_dependencies
    
    # 创建用户和目录
    create_deploy_user
    create_deploy_directory
    
    # 部署应用
    copy_project_files
    create_virtual_environment
    create_config_files
    
    # 配置系统服务
    create_systemd_service
    create_logrotate_config
    setup_cron_jobs
    
    # 创建管理工具
    create_management_scripts
    
    # 运行测试
    run_initial_test
    
    # 显示部署信息
    show_deployment_info
    
    log_info "部署完成！"
}

# 显示帮助信息
show_help() {
    cat << EOF
知识星球数据同步系统部署脚本

用法: sudo $0 [选项]

选项:
  -h, --help              显示此帮助信息
  -u, --user USER         指定部署用户 (默认: zsxq)
  -d, --deploy-dir DIR    指定部署目录 (默认: /opt/zsxq-tool)

环境变量:
  DEPLOY_USER             部署用户名
  DEPLOY_DIR              部署目录
  PYTHON_VERSION          Python 版本

示例:
  sudo $0                                    # 使用默认设置
  sudo $0 -u myuser -d /home/<USER>/zsxq     # 自定义用户和目录

注意:
  - 此脚本需要 root 权限运行
  - 部署完成后请编辑 .env 文件配置生产环境参数

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--user)
            DEPLOY_USER="$2"
            shift 2
            ;;
        -d|--deploy-dir)
            DEPLOY_DIR="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
