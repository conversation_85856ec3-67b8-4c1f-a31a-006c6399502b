#!/bin/bash
# -*- coding: utf-8 -*-
"""
快速部署脚本
用于在服务器上快速部署知识星球数据同步系统
"""

set -e

# 配置变量
SERVER_IP="**************"
SERVER_USER="ubuntu"
DEPLOY_USER="zsxq"
DEPLOY_DIR="/opt/zsxq-tool"
PROJECT_NAME="zsxq-tool"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查本地环境
check_local_environment() {
    log_step "检查本地环境..."
    
    # 检查是否在项目根目录
    if [[ ! -f "main.py" || ! -d "services" ]]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查必要的文件
    if [[ ! -f "scripts/deploy.sh" ]]; then
        log_error "找不到部署脚本 scripts/deploy.sh"
        exit 1
    fi
    
    log_info "本地环境检查通过"
}

# 打包项目文件
package_project() {
    log_step "打包项目文件..."
    
    local package_name="${PROJECT_NAME}.tar.gz"
    
    # 删除旧的打包文件
    [[ -f "$package_name" ]] && rm "$package_name"
    
    # 打包项目（排除不必要的文件）
    tar --exclude='.git' \
        --exclude='__pycache__' \
        --exclude='*.pyc' \
        --exclude='.venv' \
        --exclude='data' \
        --exclude='*.log' \
        --exclude='.env' \
        --exclude='*.tar.gz' \
        -czf "$package_name" .
    
    log_info "项目打包完成: $package_name"
}

# 上传到服务器
upload_to_server() {
    log_step "上传项目到服务器..."
    
    local package_name="${PROJECT_NAME}.tar.gz"
    
    # 上传打包文件
    scp "$package_name" "${SERVER_USER}@${SERVER_IP}:~/"
    
    log_info "项目上传完成"
}

# 在服务器上部署
deploy_on_server() {
    log_step "在服务器上执行部署..."
    
    ssh "${SERVER_USER}@${SERVER_IP}" << EOF
        set -e
        
        # 解压项目
        cd ~
        if [[ -d "${PROJECT_NAME}" ]]; then
            echo "备份现有项目..."
            sudo mv "${PROJECT_NAME}" "${PROJECT_NAME}.backup.\$(date +%Y%m%d_%H%M%S)" || true
        fi
        
        tar -xzf "${PROJECT_NAME}.tar.gz"
        
        # 进入项目目录
        cd "${PROJECT_NAME}"
        
        # 运行部署脚本
        echo "开始自动部署..."
        sudo ./scripts/deploy.sh -u ${DEPLOY_USER} -d ${DEPLOY_DIR}
        
        # 清理打包文件
        rm ~/"${PROJECT_NAME}.tar.gz"
        
        echo "部署完成！"
EOF
    
    log_info "服务器部署完成"
}

# 配置环境变量提醒
show_config_reminder() {
    log_step "配置提醒"
    
    cat << EOF

${YELLOW}重要提醒：${NC}
请登录服务器配置环境变量：

${BLUE}1. 连接到服务器：${NC}
   ssh ${SERVER_USER}@${SERVER_IP}

${BLUE}2. 编辑配置文件：${NC}
   sudo -u ${DEPLOY_USER} vim ${DEPLOY_DIR}/.env

${BLUE}3. 配置以下变量：${NC}
   ZSXQ_COOKIE=你的知识星球Cookie
   ZSXQ_GROUP_ID=48415154841458
   SUPABASE_URL=你的Supabase项目URL
   SUPABASE_KEY=你的Supabase密钥
   
   # 性能优化配置
   BATCH_USER_PROCESSING=true
   LOG_LEVEL=INFO

${BLUE}4. 测试部署：${NC}
   sudo -u ${DEPLOY_USER} ${DEPLOY_DIR}/venv/bin/python ${DEPLOY_DIR}/main.py status

${BLUE}5. 查看定时任务：${NC}
   sudo -u ${DEPLOY_USER} crontab -l

${BLUE}6. 查看日志：${NC}
   tail -f ${DEPLOY_DIR}/logs/daily.log

EOF
}

# 显示帮助信息
show_help() {
    cat << EOF
知识星球数据同步系统快速部署脚本

用法: $0 [选项]

选项:
  -h, --help              显示此帮助信息
  -s, --server IP         指定服务器IP (默认: ${SERVER_IP})
  -u, --user USER         指定服务器用户 (默认: ${SERVER_USER})

示例:
  $0                                    # 使用默认设置
  $0 -s ************* -u root         # 自定义服务器和用户

注意:
  - 请确保已配置SSH密钥认证
  - 服务器需要有sudo权限
  - 部署完成后需要手动配置.env文件

EOF
}

# 主函数
main() {
    log_info "开始快速部署知识星球数据同步系统"
    log_info "目标服务器: ${SERVER_USER}@${SERVER_IP}"
    log_info "部署目录: ${DEPLOY_DIR}"
    
    # 执行部署步骤
    check_local_environment
    package_project
    upload_to_server
    deploy_on_server
    
    # 清理本地打包文件
    rm -f "${PROJECT_NAME}.tar.gz"
    
    # 显示配置提醒
    show_config_reminder
    
    log_info "快速部署完成！"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -s|--server)
            SERVER_IP="$2"
            shift 2
            ;;
        -u|--user)
            SERVER_USER="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
