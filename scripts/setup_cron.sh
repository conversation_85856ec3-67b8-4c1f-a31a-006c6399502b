#!/bin/bash
# -*- coding: utf-8 -*-
"""
设置定时任务脚本
配置不同频率的数据同步任务
"""

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
USER="${CRON_USER:-$(whoami)}"
PYTHON_PATH="${PROJECT_DIR}/.venv/bin/python"
SYNC_SCRIPT="${PROJECT_DIR}/scripts/scheduled_sync.py"
LOG_DIR="${PROJECT_DIR}/logs"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if [ ! -f "$PYTHON_PATH" ]; then
        log_error "Python 虚拟环境不存在: $PYTHON_PATH"
        log_info "请先运行: python -m venv ${PROJECT_DIR}/.venv"
        exit 1
    fi
    
    if [ ! -f "$SYNC_SCRIPT" ]; then
        log_error "同步脚本不存在: $SYNC_SCRIPT"
        exit 1
    fi
    
    # 创建日志目录
    mkdir -p "$LOG_DIR"
    
    log_info "依赖检查通过"
}

# 生成 crontab 内容
generate_crontab() {
    local temp_file="/tmp/zsxq_crontab_$$"
    
    cat > "$temp_file" << EOF
# 知识星球数据同步定时任务
# 由 setup_cron.sh 自动生成，请勿手动修改

# 每小时同步（过去1小时的新内容）- 包含数据清洗
0 * * * * cd $PROJECT_DIR && $PYTHON_PATH main.py sync -r hour --skip-users --skip-group >> $LOG_DIR/hourly.log 2>&1

# 每天凌晨2点同步（今天所有内容）- 包含数据清洗
0 2 * * * cd $PROJECT_DIR && $PYTHON_PATH main.py sync -r day --skip-group >> $LOG_DIR/daily.log 2>&1

# 每周日凌晨3点同步（过去一周所有内容）- 包含数据清洗
0 3 * * 0 cd $PROJECT_DIR && $PYTHON_PATH main.py sync -r week >> $LOG_DIR/weekly.log 2>&1

# 每月1号凌晨4点同步（过去一个月所有内容）- 包含数据清洗
0 4 1 * * cd $PROJECT_DIR && $PYTHON_PATH main.py sync -r month >> $LOG_DIR/monthly.log 2>&1

# 每季度第一天凌晨5点全量同步（可选，根据需要启用）
# 0 5 1 1,4,7,10 * cd $PROJECT_DIR && $PYTHON_PATH $SYNC_SCRIPT full >> $LOG_DIR/full.log 2>&1

# 日志清理任务（每天凌晨1点清理30天前的日志）
0 1 * * * find $LOG_DIR -name "*.log" -mtime +30 -delete

# 数据文件清理任务（每周清理旧的数据文件）
0 1 * * 1 cd $PROJECT_DIR && find data/ -name "zsxq_hourly_*.jsonl" -mtime +7 -delete
0 1 * * 1 cd $PROJECT_DIR && find data/ -name "zsxq_daily_*.jsonl" -mtime +30 -delete
0 1 * * 1 cd $PROJECT_DIR && find data/ -name "zsxq_weekly_*.jsonl" -mtime +60 -delete

# 健康检查任务（每2小时检查一次）
0 */2 * * * cd $PROJECT_DIR && $PYTHON_PATH scripts/health_check.py >> $LOG_DIR/health.log 2>&1

# 备份任务（每天凌晨6点备份）
0 6 * * * cd $PROJECT_DIR && ./scripts/backup.sh >> $LOG_DIR/backup.log 2>&1

EOF
    
    echo "$temp_file"
}

# 安装 crontab
install_crontab() {
    log_info "为用户 $USER 安装定时任务..."
    
    # 生成新的 crontab 内容
    local temp_file
    temp_file=$(generate_crontab)
    
    # 备份现有的 crontab
    local backup_file="/tmp/crontab_backup_$(date +%Y%m%d_%H%M%S)"
    if crontab -l > "$backup_file" 2>/dev/null; then
        log_info "已备份现有 crontab 到: $backup_file"
    fi
    
    # 检查是否已存在知识星球相关任务
    if crontab -l 2>/dev/null | grep -q "知识星球数据同步"; then
        log_warn "检测到已存在知识星球相关定时任务"
        read -p "是否要替换现有任务？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "取消安装"
            rm "$temp_file"
            return 0
        fi
        
        # 移除现有的知识星球任务
        crontab -l 2>/dev/null | grep -v "知识星球" | grep -v "zsxq" > "${temp_file}.clean" || true
        cat "$temp_file" >> "${temp_file}.clean"
        mv "${temp_file}.clean" "$temp_file"
    fi
    
    # 安装新的 crontab
    crontab "$temp_file"
    
    # 清理临时文件
    rm "$temp_file"
    
    log_info "定时任务安装完成"
}

# 显示当前任务
show_current_tasks() {
    log_info "当前定时任务:"
    echo "----------------------------------------"
    crontab -l 2>/dev/null | grep -E "(知识星球|zsxq|#)" || echo "没有找到相关任务"
    echo "----------------------------------------"
}

# 测试任务
test_tasks() {
    log_info "测试同步脚本..."
    
    # 测试小时同步
    log_info "测试小时同步..."
    if cd "$PROJECT_DIR" && "$PYTHON_PATH" main.py sync -r hour --skip-download --skip-users --skip-group; then
        log_info "✅ 小时同步测试通过"
    else
        log_error "❌ 小时同步测试失败"
        return 1
    fi
    
    # 测试健康检查
    log_info "测试健康检查..."
    if cd "$PROJECT_DIR" && "$PYTHON_PATH" scripts/health_check.py; then
        log_info "✅ 健康检查测试通过"
    else
        log_warn "⚠️ 健康检查测试有警告"
    fi
    
    log_info "测试完成"
}

# 移除任务
remove_tasks() {
    log_info "移除知识星球相关定时任务..."
    
    # 备份现有的 crontab
    local backup_file="/tmp/crontab_backup_$(date +%Y%m%d_%H%M%S)"
    if crontab -l > "$backup_file" 2>/dev/null; then
        log_info "已备份现有 crontab 到: $backup_file"
    fi
    
    # 移除知识星球相关任务
    crontab -l 2>/dev/null | grep -v "知识星球" | grep -v "zsxq" | crontab - || true
    
    log_info "定时任务移除完成"
}

# 显示任务状态
show_status() {
    log_info "定时任务状态:"
    echo "----------------------------------------"
    
    # 检查 cron 服务状态
    if systemctl is-active --quiet cron 2>/dev/null || systemctl is-active --quiet crond 2>/dev/null; then
        echo "✅ Cron 服务运行正常"
    else
        echo "❌ Cron 服务未运行"
    fi
    
    # 检查任务数量
    local task_count
    task_count=$(crontab -l 2>/dev/null | grep -c "zsxq" || echo "0")
    echo "📋 知识星球相关任务数量: $task_count"
    
    # 检查最近的日志
    if [ -d "$LOG_DIR" ]; then
        echo "📊 最近的日志文件:"
        ls -lt "$LOG_DIR"/*.log 2>/dev/null | head -5 || echo "  没有找到日志文件"
    fi
    
    echo "----------------------------------------"
}

# 显示帮助
show_help() {
    cat << EOF
知识星球定时任务管理脚本

用法: $0 [命令] [选项]

命令:
  install     安装定时任务
  remove      移除定时任务
  show        显示当前任务
  status      显示任务状态
  test        测试同步脚本
  help        显示此帮助信息

选项:
  --user USER     指定用户 (默认: 当前用户)
  --dry-run       只显示将要执行的操作，不实际执行

环境变量:
  CRON_USER       定时任务用户
  PROJECT_DIR     项目目录

定时任务说明:
  - 每小时: 同步过去1小时的新内容
  - 每天: 同步今天所有内容
  - 每周: 同步过去一周所有内容
  - 每月: 同步过去一个月所有内容
  - 全量: 同步所有历史数据（可选）

示例:
  $0 install                    # 安装定时任务
  $0 show                       # 显示当前任务
  $0 test                       # 测试同步脚本
  $0 remove                     # 移除定时任务
  $0 status                     # 显示任务状态

EOF
}

# 主函数
main() {
    local command="${1:-help}"
    local dry_run=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --user)
                USER="$2"
                shift 2
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            install|remove|show|status|test|help)
                command="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示配置信息
    log_info "配置信息:"
    echo "  用户: $USER"
    echo "  项目目录: $PROJECT_DIR"
    echo "  Python路径: $PYTHON_PATH"
    echo "  日志目录: $LOG_DIR"
    echo ""
    
    if [ "$dry_run" = true ]; then
        log_warn "DRY RUN 模式 - 只显示操作，不实际执行"
    fi
    
    # 执行命令
    case $command in
        install)
            check_dependencies
            if [ "$dry_run" = false ]; then
                install_crontab
            else
                log_info "将要安装的定时任务:"
                generate_crontab | cat
            fi
            ;;
        remove)
            if [ "$dry_run" = false ]; then
                remove_tasks
            else
                log_info "将要移除知识星球相关的定时任务"
            fi
            ;;
        show)
            show_current_tasks
            ;;
        status)
            show_status
            ;;
        test)
            check_dependencies
            if [ "$dry_run" = false ]; then
                test_tasks
            else
                log_info "将要测试同步脚本"
            fi
            ;;
        help)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
