#!/bin/bash
# -*- coding: utf-8 -*-
"""
生产环境数据备份脚本
用于备份重要数据和配置文件
"""

set -e  # 遇到错误立即退出

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_BASE_DIR="${BACKUP_BASE_DIR:-/backup/zsxq}"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS="${RETENTION_DAYS:-7}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v tar &> /dev/null; then
        log_error "tar 命令未找到"
        exit 1
    fi
    
    if ! command -v gzip &> /dev/null; then
        log_error "gzip 命令未找到"
        exit 1
    fi
    
    log_info "依赖检查通过"
}

# 创建备份目录
create_backup_dir() {
    local backup_dir="$BACKUP_BASE_DIR/$DATE"
    
    log_info "创建备份目录: $backup_dir"
    mkdir -p "$backup_dir"
    
    echo "$backup_dir"
}

# 备份数据文件
backup_data_files() {
    local backup_dir="$1"
    local data_dir="$PROJECT_DIR/data"
    
    log_info "备份数据文件..."
    
    if [ -d "$data_dir" ] && [ "$(ls -A "$data_dir" 2>/dev/null)" ]; then
        local data_backup="$backup_dir/data_$DATE.tar.gz"
        tar -czf "$data_backup" -C "$PROJECT_DIR" data/
        
        local size=$(du -h "$data_backup" | cut -f1)
        log_info "数据文件备份完成: $data_backup ($size)"
    else
        log_warn "数据目录为空或不存在，跳过数据文件备份"
    fi
}

# 备份配置文件
backup_config_files() {
    local backup_dir="$1"
    
    log_info "备份配置文件..."
    
    # 备份 .env 文件（如果存在）
    if [ -f "$PROJECT_DIR/.env" ]; then
        cp "$PROJECT_DIR/.env" "$backup_dir/env_$DATE"
        log_info "环境配置备份完成: env_$DATE"
    else
        log_warn ".env 文件不存在，跳过环境配置备份"
    fi
    
    # 备份其他重要配置文件
    local config_files=("requirements.txt" "README.md")
    for file in "${config_files[@]}"; do
        if [ -f "$PROJECT_DIR/$file" ]; then
            cp "$PROJECT_DIR/$file" "$backup_dir/"
            log_info "配置文件备份: $file"
        fi
    done
}

# 备份处理状态
backup_state_files() {
    local backup_dir="$1"
    
    log_info "备份处理状态..."
    
    # 备份处理状态文件
    if [ -f "$PROJECT_DIR/processed_state.json" ]; then
        cp "$PROJECT_DIR/processed_state.json" "$backup_dir/state_$DATE.json"
        log_info "处理状态备份完成: state_$DATE.json"
    else
        log_warn "处理状态文件不存在，跳过状态备份"
    fi
    
    # 备份健康检查结果（如果存在）
    if [ -f "$PROJECT_DIR/health_check_result.json" ]; then
        cp "$PROJECT_DIR/health_check_result.json" "$backup_dir/health_check_$DATE.json"
        log_info "健康检查结果备份完成: health_check_$DATE.json"
    fi
}

# 备份数据库结构
backup_database_schema() {
    local backup_dir="$1"
    
    log_info "备份数据库结构..."
    
    if [ -f "$PROJECT_DIR/supabase.sql" ]; then
        cp "$PROJECT_DIR/supabase.sql" "$backup_dir/schema_$DATE.sql"
        log_info "数据库结构备份完成: schema_$DATE.sql"
    else
        log_warn "数据库结构文件不存在，跳过结构备份"
    fi
}

# 创建备份清单
create_backup_manifest() {
    local backup_dir="$1"
    local manifest_file="$backup_dir/MANIFEST_$DATE.txt"
    
    log_info "创建备份清单..."
    
    cat > "$manifest_file" << EOF
知识星球数据同步系统备份清单
=====================================
备份时间: $(date)
备份目录: $backup_dir
项目目录: $PROJECT_DIR

备份内容:
EOF
    
    # 列出备份文件
    find "$backup_dir" -type f -exec basename {} \; | sort >> "$manifest_file"
    
    # 添加文件大小信息
    echo "" >> "$manifest_file"
    echo "文件大小:" >> "$manifest_file"
    du -h "$backup_dir"/* >> "$manifest_file" 2>/dev/null || true
    
    log_info "备份清单创建完成: MANIFEST_$DATE.txt"
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理 $RETENTION_DAYS 天前的旧备份..."
    
    if [ -d "$BACKUP_BASE_DIR" ]; then
        # 删除旧的备份目录
        find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -mtime +$RETENTION_DAYS -exec rm -rf {} \; 2>/dev/null || true
        
        # 删除旧的单独备份文件
        find "$BACKUP_BASE_DIR" -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
        find "$BACKUP_BASE_DIR" -name "state_*.json" -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
        find "$BACKUP_BASE_DIR" -name "env_*" -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
        
        log_info "旧备份清理完成"
    else
        log_warn "备份基础目录不存在，跳过清理"
    fi
}

# 验证备份
verify_backup() {
    local backup_dir="$1"
    
    log_info "验证备份完整性..."
    
    local error_count=0
    
    # 检查备份目录是否存在
    if [ ! -d "$backup_dir" ]; then
        log_error "备份目录不存在: $backup_dir"
        ((error_count++))
    fi
    
    # 检查是否有备份文件
    local file_count=$(find "$backup_dir" -type f | wc -l)
    if [ "$file_count" -eq 0 ]; then
        log_error "备份目录为空"
        ((error_count++))
    else
        log_info "备份文件数量: $file_count"
    fi
    
    # 验证 tar 文件完整性
    for tar_file in "$backup_dir"/*.tar.gz; do
        if [ -f "$tar_file" ]; then
            if tar -tzf "$tar_file" >/dev/null 2>&1; then
                log_info "压缩文件验证通过: $(basename "$tar_file")"
            else
                log_error "压缩文件损坏: $(basename "$tar_file")"
                ((error_count++))
            fi
        fi
    done
    
    if [ $error_count -eq 0 ]; then
        log_info "备份验证通过"
        return 0
    else
        log_error "备份验证失败，发现 $error_count 个错误"
        return 1
    fi
}

# 发送通知（可选）
send_notification() {
    local status="$1"
    local backup_dir="$2"
    
    # 这里可以添加邮件、Slack、钉钉等通知逻辑
    # 示例：发送邮件通知
    if command -v mail &> /dev/null; then
        local subject="知识星球备份${status}"
        local body="备份时间: $(date)\n备份目录: $backup_dir\n状态: $status"
        
        echo -e "$body" | mail -s "$subject" <EMAIL> 2>/dev/null || true
    fi
}

# 主函数
main() {
    log_info "开始执行备份任务..."
    log_info "项目目录: $PROJECT_DIR"
    log_info "备份基础目录: $BACKUP_BASE_DIR"
    log_info "保留天数: $RETENTION_DAYS"
    
    # 检查依赖
    check_dependencies
    
    # 创建备份目录
    local backup_dir
    backup_dir=$(create_backup_dir)
    
    # 执行备份
    backup_data_files "$backup_dir"
    backup_config_files "$backup_dir"
    backup_state_files "$backup_dir"
    backup_database_schema "$backup_dir"
    
    # 创建备份清单
    create_backup_manifest "$backup_dir"
    
    # 验证备份
    if verify_backup "$backup_dir"; then
        log_info "备份任务完成: $backup_dir"
        
        # 清理旧备份
        cleanup_old_backups
        
        # 发送成功通知
        send_notification "成功" "$backup_dir"
        
        # 显示备份大小
        local total_size=$(du -sh "$backup_dir" | cut -f1)
        log_info "备份总大小: $total_size"
        
        exit 0
    else
        log_error "备份验证失败"
        send_notification "失败" "$backup_dir"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
知识星球数据同步系统备份脚本

用法: $0 [选项]

选项:
  -h, --help              显示此帮助信息
  -d, --backup-dir DIR    指定备份目录 (默认: /backup/zsxq)
  -r, --retention DAYS    备份保留天数 (默认: 7)

环境变量:
  BACKUP_BASE_DIR         备份基础目录
  RETENTION_DAYS          备份保留天数

示例:
  $0                                    # 使用默认设置
  $0 -d /custom/backup -r 14           # 自定义备份目录和保留天数
  BACKUP_BASE_DIR=/tmp/backup $0       # 使用环境变量

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--backup-dir)
            BACKUP_BASE_DIR="$2"
            shift 2
            ;;
        -r|--retention)
            RETENTION_DAYS="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
