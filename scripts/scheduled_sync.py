#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时同步脚本
支持不同时间范围的数据同步：小时、日、周、月、全量
"""

import sys
import argparse
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from workflows import DownloadWorkflow, UploadWorkflow
from utils import get_logger

logger = get_logger(__name__)


class ScheduledSync:
    """定时同步管理器"""
    
    def __init__(self):
        self.download_workflow = DownloadWorkflow()
        self.upload_workflow = UploadWorkflow()
    
    def calculate_start_time(self, sync_type: str) -> str:
        """根据同步类型计算起始时间"""
        now = datetime.now()
        
        if sync_type == "hourly":
            # 过去1小时
            start_time = now - timedelta(hours=1)
        elif sync_type == "daily":
            # 今天00:00开始
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
        elif sync_type == "weekly":
            # 过去7天
            start_time = now - timedelta(days=7)
        elif sync_type == "monthly":
            # 过去30天
            start_time = now - timedelta(days=30)
        elif sync_type == "full":
            # 全量同步，不设置起始时间
            return None
        else:
            raise ValueError(f"不支持的同步类型: {sync_type}")
        
        # 转换为知识星球API需要的格式
        return start_time.strftime("%Y-%m-%dT%H:%M:%S.000Z")
    
    def generate_filename(self, sync_type: str) -> str:
        """生成文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"zsxq_{sync_type}_{timestamp}.jsonl"
    
    def run_sync(self, sync_type: str, upload_only: bool = False) -> dict:
        """执行同步"""
        logger.info(f"开始执行 {sync_type} 同步...")
        
        result = {
            "sync_type": sync_type,
            "start_time": datetime.now().isoformat(),
            "download_success": False,
            "upload_success": False,
            "download_count": 0,
            "upload_stats": {},
            "file_path": None,
            "error_message": None
        }
        
        try:
            if not upload_only:
                # 1. 下载数据
                logger.info(f"步骤1: 下载 {sync_type} 数据")
                
                start_time = self.calculate_start_time(sync_type)
                filename = self.generate_filename(sync_type)
                
                download_result = self.download_workflow.download_data(
                    output_file=filename,
                    start_time=start_time
                )
                
                result["download_success"] = download_result.success
                result["download_count"] = download_result.total_count
                result["file_path"] = download_result.file_path
                
                if not download_result.success:
                    result["error_message"] = download_result.error_message
                    logger.error(f"下载失败: {download_result.error_message}")
                    return result
                
                logger.info(f"下载完成: {download_result.total_count} 条数据")
            else:
                # 使用最新的数据文件
                files = self.download_workflow.list_data_files()
                if not files:
                    result["error_message"] = "没有找到数据文件"
                    logger.error("没有找到数据文件")
                    return result
                
                result["file_path"] = str(Path(self.download_workflow.data_dir) / files[0])
                result["download_success"] = True
                logger.info(f"使用现有数据文件: {files[0]}")
            
            # 2. 上传数据
            logger.info(f"步骤2: 上传 {sync_type} 数据")
            
            # 根据同步类型决定是否跳过某些处理
            skip_users = sync_type == "hourly"  # 小时同步跳过用户处理
            skip_group = sync_type in ["hourly", "daily"]  # 小时和日同步跳过星球处理
            
            upload_stats = self.upload_workflow.upload_data(
                file_path=result["file_path"],
                skip_users=skip_users,
                skip_group=skip_group,
                use_new_user_workflow=True  # 使用新的用户处理工作流
            )
            
            result["upload_success"] = True
            result["upload_stats"] = upload_stats
            
            logger.info(f"上传完成: {upload_stats}")
            
        except Exception as e:
            result["error_message"] = str(e)
            logger.error(f"同步过程中发生异常: {e}")
            import traceback
            traceback.print_exc()
        
        result["end_time"] = datetime.now().isoformat()
        result["duration"] = (datetime.fromisoformat(result["end_time"]) - 
                            datetime.fromisoformat(result["start_time"])).total_seconds()
        
        return result
    
    def cleanup_old_files(self, sync_type: str, keep_count: int = 5):
        """清理旧的数据文件"""
        try:
            data_dir = Path(self.download_workflow.data_dir)
            pattern = f"zsxq_{sync_type}_*.jsonl"
            
            # 找到匹配的文件并按修改时间排序
            files = sorted(data_dir.glob(pattern), key=lambda x: x.stat().st_mtime, reverse=True)
            
            # 保留最新的几个文件，删除其余的
            files_to_delete = files[keep_count:]
            
            for file_path in files_to_delete:
                file_path.unlink()
                logger.info(f"删除旧文件: {file_path.name}")
            
            if files_to_delete:
                logger.info(f"清理完成，删除了 {len(files_to_delete)} 个旧文件")
            
        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")
    
    def save_sync_log(self, result: dict):
        """保存同步日志"""
        try:
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)
            
            log_file = log_dir / f"sync_{result['sync_type']}.log"
            
            log_entry = {
                "timestamp": result["start_time"],
                "sync_type": result["sync_type"],
                "success": result["download_success"] and result["upload_success"],
                "download_count": result["download_count"],
                "upload_stats": result["upload_stats"],
                "duration": result.get("duration", 0),
                "error": result.get("error_message")
            }
            
            # 追加到日志文件
            import json
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
            
            logger.debug(f"同步日志已保存到: {log_file}")
            
        except Exception as e:
            logger.error(f"保存同步日志失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="知识星球定时同步脚本")
    parser.add_argument('sync_type', choices=['hourly', 'daily', 'weekly', 'monthly', 'full'],
                       help='同步类型')
    parser.add_argument('--upload-only', action='store_true',
                       help='只执行上传，不下载新数据')
    parser.add_argument('--no-cleanup', action='store_true',
                       help='不清理旧文件')
    parser.add_argument('--keep-files', type=int, default=5,
                       help='保留的文件数量（默认5个）')
    
    args = parser.parse_args()
    
    # 创建同步管理器
    sync_manager = ScheduledSync()
    
    # 执行同步
    result = sync_manager.run_sync(args.sync_type, args.upload_only)
    
    # 保存日志
    sync_manager.save_sync_log(result)
    
    # 清理旧文件
    if not args.no_cleanup:
        sync_manager.cleanup_old_files(args.sync_type, args.keep_files)
    
    # 输出结果
    if result["download_success"] and result["upload_success"]:
        print(f"✅ {args.sync_type} 同步成功")
        print(f"下载: {result['download_count']} 条数据")
        print(f"上传: {result['upload_stats']}")
        print(f"耗时: {result.get('duration', 0):.1f} 秒")
        sys.exit(0)
    else:
        print(f"❌ {args.sync_type} 同步失败")
        if result.get("error_message"):
            print(f"错误: {result['error_message']}")
        sys.exit(1)


if __name__ == "__main__":
    main()
