#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境监控脚本
用于监控系统运行状态和发送告警
"""

import os
import sys
import json
import time
import smtplib
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import config
from utils import get_logger

logger = get_logger(__name__)


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        self.alerts = []
        self.metrics = {}
        
        # 告警配置
        self.alert_config = {
            'email': {
                'enabled': os.getenv('ALERT_EMAIL_ENABLED', 'false').lower() == 'true',
                'smtp_server': os.getenv('ALERT_SMTP_SERVER', 'smtp.gmail.com'),
                'smtp_port': int(os.getenv('ALERT_SMTP_PORT', '587')),
                'username': os.getenv('ALERT_EMAIL_USERNAME', ''),
                'password': os.getenv('ALERT_EMAIL_PASSWORD', ''),
                'from_email': os.getenv('ALERT_FROM_EMAIL', ''),
                'to_emails': os.getenv('ALERT_TO_EMAILS', '').split(','),
            },
            'webhook': {
                'enabled': os.getenv('ALERT_WEBHOOK_ENABLED', 'false').lower() == 'true',
                'url': os.getenv('ALERT_WEBHOOK_URL', ''),
            }
        }
    
    def check_disk_usage(self) -> dict:
        """检查磁盘使用率"""
        try:
            data_dir = Path(config.app.data_dir)
            stat = os.statvfs(data_dir)
            
            total_bytes = stat.f_blocks * stat.f_frsize
            free_bytes = stat.f_bavail * stat.f_frsize
            used_bytes = total_bytes - free_bytes
            usage_percent = (used_bytes / total_bytes) * 100
            
            metric = {
                'total_gb': total_bytes / (1024**3),
                'used_gb': used_bytes / (1024**3),
                'free_gb': free_bytes / (1024**3),
                'usage_percent': usage_percent
            }
            
            # 检查告警阈值
            if usage_percent > 90:
                self.alerts.append({
                    'level': 'critical',
                    'message': f'磁盘使用率过高: {usage_percent:.1f}%',
                    'metric': metric
                })
            elif usage_percent > 80:
                self.alerts.append({
                    'level': 'warning',
                    'message': f'磁盘使用率较高: {usage_percent:.1f}%',
                    'metric': metric
                })
            
            return metric
            
        except Exception as e:
            self.alerts.append({
                'level': 'error',
                'message': f'磁盘检查失败: {e}'
            })
            return {}
    
    def check_memory_usage(self) -> dict:
        """检查内存使用率"""
        try:
            # 读取 /proc/meminfo
            with open('/proc/meminfo', 'r') as f:
                meminfo = f.read()
            
            mem_total = int([line for line in meminfo.split('\n') if 'MemTotal' in line][0].split()[1]) * 1024
            mem_available = int([line for line in meminfo.split('\n') if 'MemAvailable' in line][0].split()[1]) * 1024
            mem_used = mem_total - mem_available
            usage_percent = (mem_used / mem_total) * 100
            
            metric = {
                'total_gb': mem_total / (1024**3),
                'used_gb': mem_used / (1024**3),
                'available_gb': mem_available / (1024**3),
                'usage_percent': usage_percent
            }
            
            # 检查告警阈值
            if usage_percent > 90:
                self.alerts.append({
                    'level': 'critical',
                    'message': f'内存使用率过高: {usage_percent:.1f}%',
                    'metric': metric
                })
            elif usage_percent > 80:
                self.alerts.append({
                    'level': 'warning',
                    'message': f'内存使用率较高: {usage_percent:.1f}%',
                    'metric': metric
                })
            
            return metric
            
        except Exception as e:
            self.alerts.append({
                'level': 'error',
                'message': f'内存检查失败: {e}'
            })
            return {}
    
    def check_service_status(self) -> dict:
        """检查服务状态"""
        try:
            # 检查 systemd 服务
            result = subprocess.run(['systemctl', 'is-active', 'zsxq-sync.timer'], 
                                  capture_output=True, text=True)
            
            timer_active = result.returncode == 0
            timer_status = result.stdout.strip() if result.returncode == 0 else 'inactive'
            
            # 检查最后执行时间
            result = subprocess.run(['systemctl', 'show', 'zsxq-sync.service', '--property=ActiveEnterTimestamp'], 
                                  capture_output=True, text=True)
            
            last_run = None
            if result.returncode == 0:
                timestamp_line = result.stdout.strip()
                if '=' in timestamp_line:
                    timestamp_str = timestamp_line.split('=', 1)[1]
                    if timestamp_str and timestamp_str != 'n/a':
                        try:
                            last_run = datetime.strptime(timestamp_str, '%a %Y-%m-%d %H:%M:%S %Z')
                        except ValueError:
                            pass
            
            metric = {
                'timer_active': timer_active,
                'timer_status': timer_status,
                'last_run': last_run.isoformat() if last_run else None
            }
            
            # 检查告警条件
            if not timer_active:
                self.alerts.append({
                    'level': 'critical',
                    'message': '定时服务未运行',
                    'metric': metric
                })
            elif last_run and (datetime.now() - last_run).days > 1:
                self.alerts.append({
                    'level': 'warning',
                    'message': f'服务超过1天未运行 (最后运行: {last_run})',
                    'metric': metric
                })
            
            return metric
            
        except Exception as e:
            self.alerts.append({
                'level': 'error',
                'message': f'服务状态检查失败: {e}'
            })
            return {}
    
    def check_data_freshness(self) -> dict:
        """检查数据新鲜度"""
        try:
            from workflows import DownloadWorkflow
            workflow = DownloadWorkflow()
            files = workflow.list_data_files()
            
            if not files:
                self.alerts.append({
                    'level': 'warning',
                    'message': '没有找到数据文件'
                })
                return {'file_count': 0}
            
            # 检查最新文件
            latest_file = files[0]
            file_path = Path(config.app.data_dir) / latest_file
            mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
            age_hours = (datetime.now() - mtime).total_seconds() / 3600
            
            metric = {
                'file_count': len(files),
                'latest_file': latest_file,
                'last_modified': mtime.isoformat(),
                'age_hours': age_hours
            }
            
            # 检查告警条件
            if age_hours > 48:  # 超过2天
                self.alerts.append({
                    'level': 'critical',
                    'message': f'数据文件过旧 ({age_hours:.1f}小时前)',
                    'metric': metric
                })
            elif age_hours > 24:  # 超过1天
                self.alerts.append({
                    'level': 'warning',
                    'message': f'数据文件较旧 ({age_hours:.1f}小时前)',
                    'metric': metric
                })
            
            return metric
            
        except Exception as e:
            self.alerts.append({
                'level': 'error',
                'message': f'数据新鲜度检查失败: {e}'
            })
            return {}
    
    def check_sync_progress(self) -> dict:
        """检查同步进度"""
        try:
            from services import StateManager
            state_manager = StateManager()
            state = state_manager.load_progress()
            
            metric = {
                'processed_users': len(state.processed_user_ids),
                'processed_topics': len(state.processed_topic_ids),
                'last_update': state.last_update
            }
            
            # 检查最后更新时间
            if state.last_update:
                last_update = datetime.fromisoformat(state.last_update.replace('Z', '+00:00'))
                age_hours = (datetime.now() - last_update.replace(tzinfo=None)).total_seconds() / 3600
                
                if age_hours > 72:  # 超过3天
                    self.alerts.append({
                        'level': 'warning',
                        'message': f'同步进度长时间未更新 ({age_hours:.1f}小时前)',
                        'metric': metric
                    })
            
            return metric
            
        except Exception as e:
            self.alerts.append({
                'level': 'error',
                'message': f'同步进度检查失败: {e}'
            })
            return {}
    
    def send_email_alert(self, subject: str, body: str):
        """发送邮件告警"""
        if not self.alert_config['email']['enabled']:
            return
        
        try:
            msg = MIMEMultipart()
            msg['From'] = self.alert_config['email']['from_email']
            msg['To'] = ', '.join(self.alert_config['email']['to_emails'])
            msg['Subject'] = subject
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(self.alert_config['email']['smtp_server'], 
                                self.alert_config['email']['smtp_port'])
            server.starttls()
            server.login(self.alert_config['email']['username'], 
                        self.alert_config['email']['password'])
            
            text = msg.as_string()
            server.sendmail(self.alert_config['email']['from_email'], 
                          self.alert_config['email']['to_emails'], text)
            server.quit()
            
            logger.info("邮件告警发送成功")
            
        except Exception as e:
            logger.error(f"邮件告警发送失败: {e}")
    
    def send_webhook_alert(self, data: dict):
        """发送 Webhook 告警"""
        if not self.alert_config['webhook']['enabled']:
            return
        
        try:
            import requests
            
            response = requests.post(
                self.alert_config['webhook']['url'],
                json=data,
                timeout=10
            )
            response.raise_for_status()
            
            logger.info("Webhook 告警发送成功")
            
        except Exception as e:
            logger.error(f"Webhook 告警发送失败: {e}")
    
    def run_monitoring(self) -> dict:
        """运行监控检查"""
        logger.info("开始系统监控...")
        
        # 执行各项检查
        self.metrics['disk'] = self.check_disk_usage()
        self.metrics['memory'] = self.check_memory_usage()
        self.metrics['service'] = self.check_service_status()
        self.metrics['data'] = self.check_data_freshness()
        self.metrics['sync'] = self.check_sync_progress()
        
        # 生成监控报告
        report = {
            'timestamp': datetime.now().isoformat(),
            'metrics': self.metrics,
            'alerts': self.alerts,
            'alert_count': {
                'critical': len([a for a in self.alerts if a.get('level') == 'critical']),
                'warning': len([a for a in self.alerts if a.get('level') == 'warning']),
                'error': len([a for a in self.alerts if a.get('level') == 'error'])
            }
        }
        
        # 发送告警
        if self.alerts:
            self.send_alerts(report)
        
        # 保存监控结果
        self.save_monitoring_result(report)
        
        logger.info(f"监控完成，发现 {len(self.alerts)} 个告警")
        return report
    
    def send_alerts(self, report: dict):
        """发送告警通知"""
        critical_alerts = [a for a in self.alerts if a.get('level') == 'critical']
        warning_alerts = [a for a in self.alerts if a.get('level') == 'warning']
        
        if critical_alerts:
            subject = f"[严重] 知识星球同步系统告警 - {len(critical_alerts)}个严重问题"
        elif warning_alerts:
            subject = f"[警告] 知识星球同步系统告警 - {len(warning_alerts)}个警告"
        else:
            subject = f"[信息] 知识星球同步系统告警 - {len(self.alerts)}个问题"
        
        # 生成告警内容
        body_lines = [
            f"监控时间: {report['timestamp']}",
            f"告警总数: {len(self.alerts)}",
            "",
            "告警详情:"
        ]
        
        for alert in self.alerts:
            level_emoji = {'critical': '🔴', 'warning': '🟡', 'error': '⚠️'}.get(alert.get('level'), '📝')
            body_lines.append(f"{level_emoji} {alert['message']}")
        
        body_lines.extend([
            "",
            "系统指标:",
            f"磁盘使用: {self.metrics.get('disk', {}).get('usage_percent', 0):.1f}%",
            f"内存使用: {self.metrics.get('memory', {}).get('usage_percent', 0):.1f}%",
            f"数据文件: {self.metrics.get('data', {}).get('file_count', 0)} 个",
            f"处理进度: 用户 {self.metrics.get('sync', {}).get('processed_users', 0)}, 帖子 {self.metrics.get('sync', {}).get('processed_topics', 0)}"
        ])
        
        body = "\n".join(body_lines)
        
        # 发送邮件告警
        self.send_email_alert(subject, body)
        
        # 发送 Webhook 告警
        webhook_data = {
            'subject': subject,
            'body': body,
            'report': report
        }
        self.send_webhook_alert(webhook_data)
    
    def save_monitoring_result(self, report: dict):
        """保存监控结果"""
        try:
            result_file = Path("monitoring_result.json")
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.debug(f"监控结果已保存到: {result_file}")
            
        except Exception as e:
            logger.error(f"保存监控结果失败: {e}")


def main():
    """主函数"""
    monitor = SystemMonitor()
    report = monitor.run_monitoring()
    
    # 输出监控摘要
    print(f"监控时间: {report['timestamp']}")
    print(f"告警数量: {len(report['alerts'])}")
    
    if report['alerts']:
        print("\n告警列表:")
        for alert in report['alerts']:
            level_emoji = {'critical': '🔴', 'warning': '🟡', 'error': '⚠️'}.get(alert.get('level'), '📝')
            print(f"  {level_emoji} {alert['message']}")
    else:
        print("✅ 系统运行正常")
    
    # 根据告警级别设置退出码
    critical_count = report['alert_count']['critical']
    if critical_count > 0:
        sys.exit(2)  # 严重告警
    elif len(report['alerts']) > 0:
        sys.exit(1)  # 一般告警
    else:
        sys.exit(0)  # 正常


if __name__ == "__main__":
    main()
