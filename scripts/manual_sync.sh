#!/bin/bash
# -*- coding: utf-8 -*-
"""
手动同步脚本
提供交互式界面来执行不同类型的数据同步
"""

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PYTHON_PATH="${PROJECT_DIR}/venv/bin/python"
SYNC_SCRIPT="${PROJECT_DIR}/scripts/scheduled_sync.py"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示菜单
show_menu() {
    clear
    echo "=========================================="
    echo "      知识星球数据同步 - 手动执行"
    echo "=========================================="
    echo ""
    echo "请选择同步类型："
    echo ""
    echo "1) 小时同步 - 过去1小时的新内容"
    echo "2) 日同步   - 今天所有内容"
    echo "3) 周同步   - 过去一周所有内容"
    echo "4) 月同步   - 过去一个月所有内容"
    echo "5) 全量同步 - 所有历史数据"
    echo ""
    echo "6) 只上传   - 使用现有数据文件上传"
    echo "7) 健康检查 - 检查系统状态"
    echo "8) 查看状态 - 查看处理进度"
    echo "9) 查看日志 - 查看同步日志"
    echo ""
    echo "0) 退出"
    echo ""
    echo "=========================================="
}

# 执行同步
run_sync() {
    local sync_type="$1"
    local upload_only="${2:-false}"
    
    log_step "准备执行 $sync_type 同步..."
    
    # 检查依赖
    if [ ! -f "$PYTHON_PATH" ]; then
        log_error "Python 虚拟环境不存在: $PYTHON_PATH"
        return 1
    fi
    
    if [ ! -f "$SYNC_SCRIPT" ]; then
        log_error "同步脚本不存在: $SYNC_SCRIPT"
        return 1
    fi
    
    # 显示同步信息
    case $sync_type in
        "hourly")
            echo "📅 时间范围: 过去1小时"
            echo "📊 处理策略: 跳过用户处理，只处理新帖子"
            ;;
        "daily")
            echo "📅 时间范围: 今天00:00至现在"
            echo "📊 处理策略: 跳过星球处理，处理用户和帖子"
            ;;
        "weekly")
            echo "📅 时间范围: 过去7天"
            echo "📊 处理策略: 完整处理所有数据"
            ;;
        "monthly")
            echo "📅 时间范围: 过去30天"
            echo "📊 处理策略: 完整处理所有数据"
            ;;
        "full")
            echo "📅 时间范围: 所有历史数据"
            echo "📊 处理策略: 完整处理所有数据"
            echo "⚠️  注意: 全量同步可能需要很长时间"
            ;;
    esac
    
    echo ""
    read -p "确认执行吗？(y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "取消执行"
        return 0
    fi
    
    # 执行同步
    log_step "开始执行同步..."
    
    local cmd="cd $PROJECT_DIR && $PYTHON_PATH $SYNC_SCRIPT $sync_type"
    if [ "$upload_only" = "true" ]; then
        cmd="$cmd --upload-only"
    fi
    
    echo "执行命令: $cmd"
    echo ""
    
    if eval "$cmd"; then
        log_info "✅ 同步执行成功"
    else
        log_error "❌ 同步执行失败"
        return 1
    fi
}

# 健康检查
run_health_check() {
    log_step "执行健康检查..."
    
    if [ ! -f "$PROJECT_DIR/scripts/health_check.py" ]; then
        log_error "健康检查脚本不存在"
        return 1
    fi
    
    cd "$PROJECT_DIR" && "$PYTHON_PATH" scripts/health_check.py
}

# 查看状态
show_status() {
    log_step "查看处理状态..."
    
    cd "$PROJECT_DIR" && "$PYTHON_PATH" main.py upload --status
}

# 查看日志
show_logs() {
    local log_dir="$PROJECT_DIR/logs"
    
    if [ ! -d "$log_dir" ]; then
        log_warn "日志目录不存在: $log_dir"
        return 1
    fi
    
    echo "可用的日志文件："
    echo "=================="
    
    local log_files=($(ls -t "$log_dir"/*.log 2>/dev/null || true))
    
    if [ ${#log_files[@]} -eq 0 ]; then
        log_warn "没有找到日志文件"
        return 1
    fi
    
    for i in "${!log_files[@]}"; do
        local file="${log_files[$i]}"
        local basename=$(basename "$file")
        local size=$(du -h "$file" | cut -f1)
        local mtime=$(stat -c %y "$file" 2>/dev/null || stat -f %Sm "$file" 2>/dev/null || echo "未知")
        echo "$((i+1))) $basename ($size, $mtime)"
    done
    
    echo ""
    read -p "选择要查看的日志文件 (1-${#log_files[@]}): " choice
    
    if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le ${#log_files[@]} ]; then
        local selected_file="${log_files[$((choice-1))]}"
        echo ""
        log_info "查看日志文件: $(basename "$selected_file")"
        echo "=================================="
        
        # 显示最后50行
        tail -50 "$selected_file"
        
        echo ""
        echo "=================================="
        read -p "按回车键继续..." -r
    else
        log_warn "无效选择"
    fi
}

# 主循环
main_loop() {
    while true; do
        show_menu
        read -p "请选择 (0-9): " choice
        
        case $choice in
            1)
                run_sync "hourly"
                ;;
            2)
                run_sync "daily"
                ;;
            3)
                run_sync "weekly"
                ;;
            4)
                run_sync "monthly"
                ;;
            5)
                run_sync "full"
                ;;
            6)
                echo ""
                echo "只上传模式 - 选择同步类型："
                echo "1) 小时同步"
                echo "2) 日同步"
                echo "3) 周同步"
                echo "4) 月同步"
                echo "5) 全量同步"
                read -p "选择 (1-5): " upload_choice
                
                case $upload_choice in
                    1) run_sync "hourly" true ;;
                    2) run_sync "daily" true ;;
                    3) run_sync "weekly" true ;;
                    4) run_sync "monthly" true ;;
                    5) run_sync "full" true ;;
                    *) log_warn "无效选择" ;;
                esac
                ;;
            7)
                run_health_check
                ;;
            8)
                show_status
                ;;
            9)
                show_logs
                ;;
            0)
                log_info "退出程序"
                exit 0
                ;;
            *)
                log_warn "无效选择，请重新输入"
                ;;
        esac
        
        if [ "$choice" != "9" ]; then
            echo ""
            read -p "按回车键继续..." -r
        fi
    done
}

# 命令行模式
command_line_mode() {
    local sync_type="$1"
    local upload_only="$2"
    
    case $sync_type in
        hourly|daily|weekly|monthly|full)
            run_sync "$sync_type" "$upload_only"
            ;;
        health)
            run_health_check
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        *)
            echo "用法: $0 [sync_type] [--upload-only]"
            echo ""
            echo "sync_type:"
            echo "  hourly   - 小时同步"
            echo "  daily    - 日同步"
            echo "  weekly   - 周同步"
            echo "  monthly  - 月同步"
            echo "  full     - 全量同步"
            echo "  health   - 健康检查"
            echo "  status   - 查看状态"
            echo "  logs     - 查看日志"
            echo ""
            echo "选项:"
            echo "  --upload-only  只上传，不下载"
            echo ""
            echo "示例:"
            echo "  $0                    # 交互模式"
            echo "  $0 hourly             # 执行小时同步"
            echo "  $0 daily --upload-only # 只上传日数据"
            exit 1
            ;;
    esac
}

# 主函数
main() {
    # 检查是否有命令行参数
    if [ $# -eq 0 ]; then
        # 交互模式
        main_loop
    else
        # 命令行模式
        local sync_type="$1"
        local upload_only=false
        
        if [ "$2" = "--upload-only" ]; then
            upload_only=true
        fi
        
        command_line_mode "$sync_type" "$upload_only"
    fi
}

# 执行主函数
main "$@"
