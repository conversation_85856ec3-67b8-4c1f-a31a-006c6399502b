#!/bin/bash
# -*- coding: utf-8 -*-
"""
定时任务管理脚本
用于管理知识星球数据同步系统的定时任务
"""

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PYTHON_PATH="$PROJECT_DIR/venv/bin/python"
LOG_DIR="$PROJECT_DIR/logs"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 创建定时任务内容
create_cron_content() {
    cat << EOF
# 知识星球数据同步定时任务（新版本）
# 生成时间: $(date)

# 每小时同步（过去1小时的新内容）- 包含数据清洗
0 * * * * cd $PROJECT_DIR && $PYTHON_PATH main.py sync -r hour --skip-users --skip-group >> $LOG_DIR/hourly.log 2>&1

# 每天凌晨2点同步（今天所有内容）- 包含数据清洗  
0 2 * * * cd $PROJECT_DIR && $PYTHON_PATH main.py sync -r day --skip-group >> $LOG_DIR/daily.log 2>&1

# 每周日凌晨3点同步（过去一周所有内容）- 包含数据清洗
0 3 * * 0 cd $PROJECT_DIR && $PYTHON_PATH main.py sync -r week >> $LOG_DIR/weekly.log 2>&1

# 每月1号凌晨4点同步（过去一个月所有内容）- 包含数据清洗
0 4 1 * * cd $PROJECT_DIR && $PYTHON_PATH main.py sync -r month >> $LOG_DIR/monthly.log 2>&1

# 每天凌晨5点执行备份
0 5 * * * $PROJECT_DIR/scripts/backup.sh >> $LOG_DIR/backup.log 2>&1

# 每2小时执行健康检查
0 */2 * * * $PYTHON_PATH $PROJECT_DIR/scripts/health_check.py >> $LOG_DIR/health.log 2>&1

# 日志清理任务（每天凌晨1点清理30天前的日志）
0 1 * * * find $LOG_DIR -name "*.log" -mtime +30 -delete

# 数据文件清理任务（每周清理旧的数据文件）
0 1 * * 1 cd $PROJECT_DIR && find data/ -name "zsxq_topics_*.jsonl" -mtime +7 -delete

EOF
}

# 安装定时任务
install_cron() {
    log_step "安装定时任务..."
    
    # 确保日志目录存在
    mkdir -p "$LOG_DIR"
    
    # 创建临时文件
    local temp_file=$(mktemp)
    
    # 生成定时任务内容
    create_cron_content > "$temp_file"
    
    # 安装新的 crontab
    crontab "$temp_file"
    
    # 清理临时文件
    rm "$temp_file"
    
    log_info "定时任务安装完成"
    show_current_tasks
}

# 移除定时任务
remove_cron() {
    log_step "移除定时任务..."
    
    # 备份当前的 crontab
    local backup_file="$PROJECT_DIR/crontab_backup_$(date +%Y%m%d_%H%M%S).txt"
    crontab -l > "$backup_file" 2>/dev/null || true
    
    # 移除所有定时任务
    crontab -r 2>/dev/null || true
    
    log_info "定时任务已移除"
    log_info "备份文件: $backup_file"
}

# 显示当前任务
show_current_tasks() {
    log_info "当前定时任务:"
    echo "----------------------------------------"
    crontab -l 2>/dev/null | grep -E "(知识星球|zsxq|sync|#)" || echo "没有找到相关任务"
    echo "----------------------------------------"
}

# 测试任务
test_tasks() {
    log_info "测试同步脚本..."
    
    # 测试小时同步
    log_info "测试小时同步..."
    if cd "$PROJECT_DIR" && "$PYTHON_PATH" main.py sync -r hour --skip-upload --skip-users --skip-group; then
        log_info "✅ 小时同步测试通过"
    else
        log_error "❌ 小时同步测试失败"
        return 1
    fi
    
    # 测试状态查看
    log_info "测试状态查看..."
    if cd "$PROJECT_DIR" && "$PYTHON_PATH" main.py status; then
        log_info "✅ 状态查看测试通过"
    else
        log_warn "⚠️ 状态查看测试有警告"
    fi
    
    # 测试健康检查
    log_info "测试健康检查..."
    if cd "$PROJECT_DIR" && "$PYTHON_PATH" scripts/health_check.py; then
        log_info "✅ 健康检查测试通过"
    else
        log_warn "⚠️ 健康检查测试有警告"
    fi
    
    log_info "测试完成"
}

# 查看任务状态
show_status() {
    log_info "定时任务状态:"
    
    # 显示当前任务
    show_current_tasks
    
    # 显示最近的日志
    echo ""
    log_info "最近的日志文件:"
    if [[ -d "$LOG_DIR" ]]; then
        ls -la "$LOG_DIR"/*.log 2>/dev/null | head -10 || echo "没有找到日志文件"
    else
        echo "日志目录不存在: $LOG_DIR"
    fi
    
    # 显示磁盘使用情况
    echo ""
    log_info "磁盘使用情况:"
    du -sh "$PROJECT_DIR"/{data,logs} 2>/dev/null || echo "无法获取磁盘使用情况"
}

# 查看日志
show_logs() {
    local log_type="${1:-daily}"
    local log_file="$LOG_DIR/${log_type}.log"
    
    if [[ -f "$log_file" ]]; then
        log_info "显示 $log_type 日志 (最后50行):"
        tail -50 "$log_file"
    else
        log_warn "日志文件不存在: $log_file"
        log_info "可用的日志文件:"
        ls -la "$LOG_DIR"/*.log 2>/dev/null || echo "没有找到日志文件"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
定时任务管理脚本

用法: $0 <命令> [选项]

命令:
  install                 安装定时任务
  remove                  移除定时任务
  show                    显示当前任务
  test                    测试同步脚本
  status                  查看任务状态
  logs [TYPE]             查看日志 (hourly|daily|weekly|monthly|health|backup)

示例:
  $0 install              # 安装定时任务
  $0 remove               # 移除定时任务
  $0 show                 # 显示当前任务
  $0 test                 # 测试同步功能
  $0 status               # 查看状态
  $0 logs daily           # 查看日同步日志
  $0 logs hourly          # 查看小时同步日志

注意:
  - 请在项目根目录运行此脚本
  - 确保Python虚拟环境已正确配置

EOF
}

# 主函数
main() {
    local command="${1:-help}"
    
    case "$command" in
        install)
            install_cron
            ;;
        remove)
            remove_cron
            ;;
        show)
            show_current_tasks
            ;;
        test)
            test_tasks
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "$2"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 检查是否在正确的目录
if [[ ! -f "$PROJECT_DIR/main.py" ]]; then
    log_error "请在项目根目录运行此脚本"
    exit 1
fi

# 执行主函数
main "$@"
