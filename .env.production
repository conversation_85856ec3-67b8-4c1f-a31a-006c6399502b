# 生产环境配置文件
# 复制此文件为 .env 并填入真实的生产环境配置

# ==========================================
# 知识星球配置
# ==========================================
# 从浏览器开发者工具中获取的完整 Cookie
ZSXQ_COOKIE=your_production_zsxq_access_token_here

# 知识星球群组ID
ZSXQ_GROUP_ID=48415154841458

# API请求配置
ZSXQ_COUNT=50                    # 单次请求数量（生产环境建议50-100）
ZSXQ_MAX_RETRIES=5               # 最大重试次数
ZSXQ_RETRY_WAIT_TIME=5           # 重试等待时间（秒）
ZSXQ_REQUEST_TIMEOUT=30          # 请求超时时间（秒）

# ==========================================
# Supabase 数据库配置
# ==========================================
# Supabase 项目URL
SUPABASE_URL=https://your-prod-project.supabase.co

# Supabase 匿名密钥（anon key）
SUPABASE_KEY=your_production_supabase_anon_key_here

# 存储桶配置
SUPABASE_AVATARS_BUCKET=avatars
SUPABASE_GROUP_BACKGROUNDS_BUCKET=group-backgrounds
SUPABASE_TOPIC_IMAGES_BUCKET=topic-images

# ==========================================
# 应用配置
# ==========================================
# 数据存储目录
DATA_DIR=/data/zsxq

# 处理状态文件路径
STATE_FILE=/data/zsxq/processed_state.json

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# ==========================================
# 监控告警配置
# ==========================================
# 邮件告警配置
ALERT_EMAIL_ENABLED=true
ALERT_SMTP_SERVER=smtp.gmail.com
ALERT_SMTP_PORT=587
ALERT_EMAIL_USERNAME=<EMAIL>
ALERT_EMAIL_PASSWORD=your_app_password
ALERT_FROM_EMAIL=<EMAIL>
ALERT_TO_EMAILS=<EMAIL>,<EMAIL>

# Webhook 告警配置（可选）
ALERT_WEBHOOK_ENABLED=false
ALERT_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# ==========================================
# 性能优化配置
# ==========================================
# 数据库连接池大小
DB_POOL_SIZE=10

# 并发处理数量
CONCURRENT_WORKERS=4

# 批处理大小
BATCH_SIZE=100

# ==========================================
# 安全配置
# ==========================================
# 是否启用调试模式（生产环境应为 false）
DEBUG_MODE=false

# 是否启用远程调试（生产环境应为 false）
ENABLE_REMOTE_DEBUG=false

# API 请求频率限制（请求/分钟）
API_RATE_LIMIT=60
