# -*- coding: utf-8 -*-
# @Time    : 2025/6/24
# <AUTHOR> zhuzhenglin
import re
from urllib.parse import unquote
from bs4 import BeautifulSoup

# --- 模拟从爬虫获取的、包含混合换行符的原始数据 ---

sample_data_mixed_newlines = """你好哇， 我是小翼。

财富自由真的有公式吗？

不上班两年，目前实现半fire退休

最近发现一个适用于任何人的财富密码，

那就是百万播客大佬，9家公司的老板斯科特总结的：

财富=专注+（自律x时间x分散投资）\r
\r
最终你的被动收入＞烧钱水平，那就彻底财务自由了。

如果像我一样半fire退休，只需要 ..."""


def clean_and_convert_content(raw_text: str) -> str:
    """
    一个完整的文本清洗和转换函数，将原始文本转换为干净、语义化的HTML。
    (已修正) 版本，能正确处理多种换行符 (\n, \r, \r\n)。

    :param raw_text: 从爬虫抓取的包含自定义标签和不规则换行的原始字符串。
    :return: 处理后可直接存入数据库并在前端渲染的HTML字符串。
    """
    if not isinstance(raw_text, str) or not raw_text:
        return ""

    # 步骤 1: (新增) 标准化所有换行符为 \n
    # 这是解决问题的关键步骤。
    # 先将 \r\n 替换为 \n，再将单独的 \r 替换为 \n。
    normalized_text = raw_text.replace('\r\n', '\n').replace('\r', '\n')

    # 步骤 2: 将2个及以上的连续换行符合并为标准的段落分隔符（两个换行符），并去除首尾空白。
    cleaned_text = re.sub(r'\n{2,}', '\n\n', normalized_text).strip()

    # 步骤 3: 按段落分隔符分割成段落数组。
    paragraphs = cleaned_text.split('\n\n')

    processed_paragraphs = []

    # 步骤 4: 遍历每个段落，进行内部处理。
    for p_text in paragraphs:
        # 使用 BeautifulSoup 解析每个段落，这样可以安全地处理标签
        soup = BeautifulSoup(p_text, 'html.parser')

        # 查找所有自定义的 <e> 标签
        for tag in soup.find_all('e'):
            # (此处的 <e> 标签处理逻辑保持不变)
            tag_type = tag.get('type')
            title_encoded = tag.get('title', '')
            try:
                title_decoded = unquote(title_encoded)
                if tag_type == 'web':
                    href_encoded = tag.get('href', '')
                    href_decoded = unquote(href_encoded)
                    new_a_tag = soup.new_tag('a', href=href_decoded, target='_blank', rel='noopener noreferrer')
                    new_a_tag.string = title_decoded
                    tag.replace_with(new_a_tag)
                elif tag_type == 'text_bold':
                    new_strong_tag = soup.new_tag('strong')
                    new_strong_tag.string = title_decoded
                    tag.replace_with(new_strong_tag)
                else:
                    tag.decompose()
            except Exception as e:
                print(f"处理标签时发生错误: {tag}, 错误: {e}")
                tag.decompose()

        # 将处理完 <e> 标签的段落内容转换回字符串
        processed_text = soup.decode_contents()

        # 将段落内部的单个换行符替换为 <br />
        processed_text = processed_text.replace('\n', '<br />')

        # 步骤 5: 用 <p> 标签包裹每个处理后的段落。
        processed_paragraphs.append(f'<p>{processed_text}</p>')

    # 步骤 6: 将所有处理好的段落 HTML 连接成一个完整的字符串。
    return "".join(processed_paragraphs)


# --- 主程序入口，用于测试 ---
if __name__ == "__main__":
    print("--- [开始] 测试包含混合换行符的数据 ---")
    final_html = clean_and_convert_content(sample_data_mixed_newlines)
    print("--- [预期输出] ---")
    print(
        "<p>你好哇， 我是小翼。</p><p>财富自由真的有公式吗？</p><p>不上班两年，目前实现半fire退休</p><p>最近发现一个适用于任何人的财富密码，</p><p>那就是百万播客大佬，9家公司的老板斯科特总结的：</p><p>财富=专注+（自律x时间x分散投资）</p><p>最终你的被动收入＞烧钱水平，那就彻底财务自由了。</p><p>如果像我一样半fire退休，只需要 ...</p>")
    print("\n--- [实际输出] ---")
    print(final_html)

    # 验证实际输出是否和预期一致
    expected_output = "<p>你好哇， 我是小翼。</p><p>财富自由真的有公式吗？</p><p>不上班两年，目前实现半fire退休</p><p>最近发现一个适用于任何人的财富密码，</p><p>那就是百万播客大佬，9家公司的老板斯科特总结的：</p><p>财富=专注+（自律x时间x分散投资）</p><p>最终你的被动收入＞烧钱水平，那就彻底财务自由了。</p><p>如果像我一样半fire退休，只需要 ...</p>"
    assert final_html == expected_output
    print("\n[成功] 实际输出与预期一致！问题已解决。")
