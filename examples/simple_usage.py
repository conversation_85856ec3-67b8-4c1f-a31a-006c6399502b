# -*- coding: utf-8 -*-
"""
简单使用示例
演示如何使用新架构进行数据下载和上传
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from workflows import DownloadWorkflow, UploadWorkflow
from utils import get_logger

logger = get_logger(__name__)


def example_download():
    """示例：下载数据"""
    print("=== 下载数据示例 ===")
    
    # 创建下载工作流
    download_workflow = DownloadWorkflow()
    
    # 执行下载
    result = download_workflow.download_data()
    
    if result.success:
        print(f"✅ 下载成功！")
        print(f"文件: {result.file_path}")
        print(f"数据量: {result.total_count} 条")
        return result.file_path
    else:
        print(f"❌ 下载失败: {result.error_message}")
        return None


def example_upload(file_path: str):
    """示例：上传数据"""
    print("\n=== 上传数据示例 ===")
    
    # 创建上传工作流
    upload_workflow = UploadWorkflow()
    
    # 查看当前进度
    progress = upload_workflow.get_progress_info()
    print(f"当前进度: 用户 {progress['processed_users']}, 帖子 {progress['processed_topics']}")
    
    # 执行上传
    try:
        results = upload_workflow.upload_data(file_path)
        
        print(f"✅ 上传成功！")
        print(f"处理结果:")
        print(f"  用户: {results['users_processed']}")
        print(f"  星球: {results['group_processed']}")
        print(f"  帖子: {results['topics_processed']}")
        
    except Exception as e:
        print(f"❌ 上传失败: {e}")


def example_list_files():
    """示例：列出数据文件"""
    print("\n=== 数据文件列表 ===")
    
    download_workflow = DownloadWorkflow()
    files = download_workflow.list_data_files()
    
    if files:
        print("可用的数据文件:")
        for i, file in enumerate(files, 1):
            print(f"  {i}. {file}")
    else:
        print("没有找到数据文件")


def main():
    """主函数"""
    print("知识星球数据处理工具 - 使用示例")
    print("=" * 50)
    
    # 1. 列出现有文件
    example_list_files()
    
    # 2. 下载新数据
    file_path = example_download()
    
    # 3. 上传数据（如果下载成功）
    if file_path:
        example_upload(file_path)
    else:
        # 如果下载失败，尝试使用最新的文件
        download_workflow = DownloadWorkflow()
        latest_file = download_workflow.get_latest_data_file()
        if latest_file:
            print(f"\n使用最新文件进行上传: {latest_file}")
            example_upload(latest_file)
        else:
            print("\n没有可用的数据文件进行上传")


if __name__ == "__main__":
    main()
