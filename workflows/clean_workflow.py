# -*- coding: utf-8 -*-
"""
数据清洗工作流
负责清洗知识星球的原始数据
"""

import json
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

from utils import get_logger
from services import ContentCleaner
from config import config

logger = get_logger(__name__)


class CleanWorkflow:
    """数据清洗工作流"""
    
    def __init__(self):
        self.content_cleaner = ContentCleaner()
        self.data_dir = Path(config.app.data_dir)
        
    def clean_file(self, input_file: str, output_file: Optional[str] = None, convert_to_html: bool = False) -> Dict[str, Any]:
        """
        清洗单个文件

        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径，如果为空则自动生成
            convert_to_html: 是否转换为HTML格式

        Returns:
            Dict: 清洗结果统计
        """
        input_path = Path(input_file)
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        # 生成输出文件名
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = input_path.stem + f"_cleaned_{timestamp}" + input_path.suffix
            output_path = self.data_dir / output_file
        else:
            output_path = Path(output_file)
        
        logger.info(f"开始清洗文件: {input_path}")
        logger.info(f"输出文件: {output_path}")
        
        # 确保输出目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 执行清洗
        stats = self.content_cleaner.clean_jsonl_file(str(input_path), str(output_path), convert_to_html)
        
        # 添加文件信息
        stats.update({
            'input_file': str(input_path),
            'output_file': str(output_path),
            'input_size_mb': input_path.stat().st_size / (1024 * 1024),
            'output_size_mb': output_path.stat().st_size / (1024 * 1024),
            'processing_time': datetime.now().isoformat()
        })
        
        logger.info(f"清洗完成: {stats}")
        return stats
    
    def clean_latest_file(self, output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        清洗最新的数据文件
        
        Args:
            output_file: 输出文件路径，如果为空则自动生成
            
        Returns:
            Dict: 清洗结果统计
        """
        # 查找最新的数据文件
        pattern = "zsxq_topics_*.jsonl"
        files = list(self.data_dir.glob(pattern))
        
        if not files:
            raise FileNotFoundError(f"在 {self.data_dir} 中没有找到匹配的数据文件")
        
        # 按修改时间排序，获取最新的文件
        latest_file = max(files, key=lambda x: x.stat().st_mtime)
        
        logger.info(f"找到最新文件: {latest_file}")
        return self.clean_file(str(latest_file), output_file)
    
    def clean_all_files(self, pattern: str = "zsxq_topics_*.jsonl") -> Dict[str, Any]:
        """
        清洗所有匹配的数据文件
        
        Args:
            pattern: 文件匹配模式
            
        Returns:
            Dict: 清洗结果统计
        """
        files = list(self.data_dir.glob(pattern))
        
        if not files:
            raise FileNotFoundError(f"在 {self.data_dir} 中没有找到匹配的数据文件")
        
        total_stats = {
            'total_files': len(files),
            'processed_files': 0,
            'failed_files': 0,
            'total_topics': 0,
            'total_cleaned_topics': 0,
            'total_errors': 0,
            'file_results': []
        }
        
        for file_path in files:
            try:
                logger.info(f"处理文件: {file_path}")
                
                # 生成输出文件名
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_file = file_path.stem + f"_cleaned_{timestamp}" + file_path.suffix
                output_path = self.data_dir / output_file
                
                # 清洗文件
                stats = self.content_cleaner.clean_jsonl_file(str(file_path), str(output_path))
                
                # 更新总计
                total_stats['processed_files'] += 1
                total_stats['total_topics'] += stats['total_topics']
                total_stats['total_cleaned_topics'] += stats['cleaned_topics']
                total_stats['total_errors'] += stats['errors']
                
                # 记录文件结果
                file_result = {
                    'input_file': str(file_path),
                    'output_file': str(output_path),
                    'stats': stats
                }
                total_stats['file_results'].append(file_result)
                
                logger.info(f"文件处理完成: {file_path}")
                
            except Exception as e:
                logger.error(f"处理文件失败 {file_path}: {e}")
                total_stats['failed_files'] += 1
                
                # 记录失败结果
                file_result = {
                    'input_file': str(file_path),
                    'output_file': None,
                    'error': str(e)
                }
                total_stats['file_results'].append(file_result)
        
        logger.info(f"批量清洗完成: {total_stats}")
        return total_stats
    
    def list_data_files(self, pattern: str = "zsxq_topics_*.jsonl") -> list:
        """
        列出所有数据文件
        
        Args:
            pattern: 文件匹配模式
            
        Returns:
            list: 文件列表
        """
        files = list(self.data_dir.glob(pattern))
        
        # 按修改时间排序（最新的在前）
        files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        return [f.name for f in files]
    
    def get_file_info(self, filename: str) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            filename: 文件名
            
        Returns:
            Dict: 文件信息
        """
        file_path = self.data_dir / filename
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {filename}")
        
        stat = file_path.stat()
        
        # 统计行数
        line_count = 0
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                line_count = sum(1 for _ in f)
        except Exception as e:
            logger.warning(f"无法统计行数: {e}")
        
        return {
            'filename': filename,
            'path': str(file_path),
            'size_bytes': stat.st_size,
            'size_mb': stat.st_size / (1024 * 1024),
            'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
            'line_count': line_count
        }
    
    def validate_cleaned_file(self, filename: str) -> Dict[str, Any]:
        """
        验证清洗后的文件
        
        Args:
            filename: 文件名
            
        Returns:
            Dict: 验证结果
        """
        file_path = self.data_dir / filename
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {filename}")
        
        validation_result = {
            'filename': filename,
            'valid_lines': 0,
            'invalid_lines': 0,
            'total_lines': 0,
            'errors': []
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    validation_result['total_lines'] += 1
                    
                    try:
                        data = json.loads(line.strip())
                        
                        # 基本验证
                        if not isinstance(data, dict):
                            raise ValueError("数据不是字典格式")
                        
                        if 'topic_id' not in data:
                            raise ValueError("缺少 topic_id 字段")
                        
                        validation_result['valid_lines'] += 1
                        
                    except json.JSONDecodeError as e:
                        validation_result['invalid_lines'] += 1
                        validation_result['errors'].append(f"第{line_num}行JSON解析错误: {e}")
                    except ValueError as e:
                        validation_result['invalid_lines'] += 1
                        validation_result['errors'].append(f"第{line_num}行数据验证错误: {e}")
                        
        except Exception as e:
            validation_result['errors'].append(f"文件读取错误: {e}")
        
        validation_result['is_valid'] = validation_result['invalid_lines'] == 0
        validation_result['success_rate'] = (
            validation_result['valid_lines'] / validation_result['total_lines'] * 100
            if validation_result['total_lines'] > 0 else 0
        )
        
        return validation_result
