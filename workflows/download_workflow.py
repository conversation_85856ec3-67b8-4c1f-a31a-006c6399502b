# -*- coding: utf-8 -*-
"""
下载工作流
负责协调数据爬取流程
"""

import os
from datetime import datetime
from pathlib import Path
from typing import Optional

from config import config
from utils import get_logger
from services import ZsxqCrawler
from models import CrawlResult

logger = get_logger(__name__)


class DownloadWorkflow:
    """下载工作流"""
    
    def __init__(self):
        self.crawler = ZsxqCrawler()
        self.data_dir = Path(config.app.data_dir)

    def _convert_time_format(self, time_str: str) -> str:
        """
        将用户输入的时间格式转换为 API 需要的 ISO 8601 格式

        Args:
            time_str: 用户输入的时间字符串，支持格式：
                     - YYYY-MM-DD (如: 2025-06-17)
                     - YYYY-MM-DD HH:MM:SS (如: 2025-06-17 10:30:00)

        Returns:
            str: ISO 8601 格式的时间字符串 (如: 2025-06-17T10:30:00.000+0800)
        """
        try:
            # 尝试解析不同的时间格式
            if len(time_str) == 10:  # YYYY-MM-DD
                dt = datetime.strptime(time_str, "%Y-%m-%d")
                # 设置为当天的结束时间 (23:59:59)
                dt = dt.replace(hour=23, minute=59, second=59)
            elif len(time_str) == 19:  # YYYY-MM-DD HH:MM:SS
                dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
            else:
                # 如果已经是 ISO 格式，直接返回
                return time_str

            # 转换为 ISO 8601 格式，添加时区信息 (+0800)
            iso_time = dt.strftime("%Y-%m-%dT%H:%M:%S.000+0800")
            return iso_time

        except ValueError as e:
            logger.error(f"时间格式转换失败: {time_str}, 错误: {e}")
            logger.info("支持的时间格式:")
            logger.info("  - YYYY-MM-DD (如: 2025-06-17)")
            logger.info("  - YYYY-MM-DD HH:MM:SS (如: 2025-06-17 10:30:00)")
            raise ValueError(f"不支持的时间格式: {time_str}")
        self.data_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_filename(self, prefix: str = "zsxq_topics") -> str:
        """生成文件名"""
        current_year = datetime.now().year
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{prefix}_{current_year}_{timestamp}.jsonl"
    
    def download_data(self,
                     output_file: Optional[str] = None,
                     start_time: Optional[str] = None,
                     end_time: Optional[str] = None) -> CrawlResult:
        """
        执行数据下载流程

        Args:
            output_file: 输出文件名，如果为空则自动生成
            start_time: 开始时间（时间范围下限），如果为空则无下限
            end_time: 结束时间（时间范围上限），如果为空则从最新开始

        Returns:
            CrawlResult: 下载结果
        """
        logger.info("=== 开始数据下载流程 ===")
        
        # 验证配置
        if not config.validate():
            return CrawlResult(
                success=False,
                total_count=0,
                error_message="配置验证失败"
            )
        
        # 生成输出文件路径
        if not output_file:
            output_file = self.generate_filename()
        
        output_path = self.data_dir / output_file
        
        # 转换时间格式
        converted_start_time = None
        converted_end_time = None

        if start_time:
            try:
                converted_start_time = self._convert_time_format(start_time)
                logger.info(f"开始时间格式转换: {start_time} -> {converted_start_time}")
            except ValueError as e:
                logger.error(f"开始时间格式转换失败: {e}")
                return CrawlResult(success=False, total_count=0, error_message=str(e))

        if end_time:
            try:
                converted_end_time = self._convert_time_format(end_time)
                logger.info(f"结束时间格式转换: {end_time} -> {converted_end_time}")
            except ValueError as e:
                logger.error(f"结束时间格式转换失败: {e}")
                return CrawlResult(success=False, total_count=0, error_message=str(e))

        logger.info(f"输出文件: {output_path}")
        logger.info(f"时间范围: {start_time or '无下限'} 到 {end_time or '最新'}")

        # 执行爬取
        try:
            result = self.crawler.save_to_file(str(output_path), converted_start_time, converted_end_time)
            
            if result.success:
                logger.info(f"✅ 数据下载完成，共获取 {result.total_count} 条数据")
                logger.info(f"文件保存位置: {result.file_path}")
            else:
                logger.error(f"❌ 数据下载失败: {result.error_message}")
            
            return result
            
        except Exception as e:
            logger.error(f"下载流程发生异常: {e}")
            return CrawlResult(
                success=False,
                total_count=0,
                error_message=str(e)
            )
    
    def list_data_files(self) -> list[str]:
        """列出数据目录中的所有 JSONL 文件"""
        jsonl_files = list(self.data_dir.glob("*.jsonl"))
        return [f.name for f in sorted(jsonl_files, key=lambda x: x.stat().st_mtime, reverse=True)]
    
    def get_latest_data_file(self) -> Optional[str]:
        """获取最新的数据文件"""
        files = self.list_data_files()
        return files[0] if files else None
