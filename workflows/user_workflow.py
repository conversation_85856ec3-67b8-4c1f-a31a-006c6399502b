# -*- coding: utf-8 -*-
"""
用户处理工作流
负责处理用户数据，包括获取 mid 和保存到 users_new 表
"""

import time
from typing import Dict, List, Optional, Set
from collections import defaultdict

from tqdm import tqdm

from utils import get_logger
from services import DataProcessor, StorageService, StateManager, MemberCrawler, UserCacheService
from models import User, UserNew

logger = get_logger(__name__)


class UserWorkflow:
    """用户处理工作流"""
    
    def __init__(self):
        self.processor = DataProcessor()
        self.storage = StorageService()
        self.state_manager = StateManager()
        self.member_crawler = MemberCrawler()
        self.user_cache = UserCacheService()

        # 缓存已获取的 mid 信息
        self.mid_cache: Dict[int, Optional[int]] = {}

        # 请求间隔控制
        self.request_interval = 1.0  # 秒
        self.last_request_time = 0
    
    def _rate_limit(self):
        """请求频率限制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.request_interval:
            sleep_time = self.request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def get_user_mid(self, user_id: int) -> Optional[int]:
        """
        获取用户的 mid，优先从数据缓存中查找，然后使用本地缓存避免重复请求

        Args:
            user_id: 用户 ID

        Returns:
            int: 用户的 mid，如果获取失败返回 None
        """
        # 检查本地缓存
        if user_id in self.mid_cache:
            return self.mid_cache[user_id]

        # 检查数据缓存
        cached_user_id = self.user_cache.get_user_id_by_uid(user_id)
        if cached_user_id:
            user_info = self.user_cache.get_user_info_by_id(cached_user_id)
            if user_info and user_info.get('zsxq_group_member_id'):
                mid = user_info['zsxq_group_member_id']
                # 更新本地缓存
                self.mid_cache[user_id] = mid
                logger.debug(f"从数据缓存中获取用户 {user_id} 的 mid: {mid}")
                return mid

        # 数据缓存中没有，使用爬虫获取
        logger.debug(f"用户 {user_id} 不在缓存中，使用爬虫获取 mid")

        # 频率限制
        self._rate_limit()

        # 获取 mid
        mid = self.member_crawler.get_member_mid(user_id)

        # 缓存结果
        self.mid_cache[user_id] = mid

        return mid
    
    def convert_user_to_user_new(self, user: User) -> Optional[UserNew]:
        """
        将旧的 User 对象转换为新的 UserNew 对象
        
        Args:
            user: 旧的 User 对象
            
        Returns:
            UserNew: 新的 UserNew 对象，如果转换失败返回 None
        """
        # 获取 mid
        mid = self.get_user_mid(user.user_id)
        if mid is None:
            logger.warning(f"无法获取用户 {user.user_id} 的 mid，跳过转换")
            return None
        
        return UserNew(
            zsxq_group_member_id=mid,
            user_id=user.user_id,
            name=user.name,
            alias=user.alias,
            avatar_url=user.avatar_url,
            avatar_path=user.avatar_path,
            description=user.description,
            join_time=user.join_time,
            status=user.status,
            location=user.location
        )
    
    def process_users_from_topics(self, topics_data: List[Dict]) -> Dict[str, int]:
        """
        从帖子数据中提取并处理所有用户
        
        Args:
            topics_data: 帖子数据列表
            
        Returns:
            Dict: 处理结果统计
        """
        logger.info("--- 开始从帖子数据中提取用户信息 ---")
        
        # 第一步：收集所有唯一用户
        all_users: Dict[int, User] = {}
        
        for topic_data in tqdm(topics_data, desc="收集用户信息"):
            users_in_topic = self.processor.extract_all_users_from_topic(topic_data)
            for user in users_in_topic:
                if user and user.user_id:
                    all_users[user.user_id] = user
        
        logger.info(f"收集到 {len(all_users)} 个唯一用户")

        # 第二步：从缓存中查找已存在的用户，只爬取缓存中不存在的用户
        logger.info("--- 开始检查用户缓存并获取缺失的 mid 信息 ---")

        user_ids = list(all_users.keys())
        cached_count = 0
        need_crawl_ids = []

        # 检查哪些用户已在缓存中
        for uid in user_ids:
            cached_user_id = self.user_cache.get_user_id_by_uid(uid)
            if cached_user_id:
                # 从缓存中获取 mid 信息
                user_info = self.user_cache.get_user_info_by_id(cached_user_id)
                if user_info and user_info.get('zsxq_group_member_id'):
                    self.mid_cache[uid] = user_info['zsxq_group_member_id']
                    cached_count += 1
                else:
                    need_crawl_ids.append(uid)
            else:
                need_crawl_ids.append(uid)

        logger.info(f"从缓存中找到 {cached_count} 个用户，需要爬取 {len(need_crawl_ids)} 个用户")

        # 只爬取缓存中不存在的用户
        if need_crawl_ids:
            logger.info(f"开始爬取 {len(need_crawl_ids)} 个缺失用户的 mid 信息...")
            mid_results = self.member_crawler.batch_get_member_mids(need_crawl_ids, self.request_interval)
            # 更新缓存
            self.mid_cache.update(mid_results)
        else:
            logger.info("所有用户都已在缓存中，无需爬取")
        
        # 第三步：只处理和保存新获取的用户（从爬虫获取的用户）
        logger.info("--- 开始转换并保存新用户到 users 表 ---")

        success_count = 0
        failed_count = 0
        skipped_count = 0
        cached_users_count = cached_count  # 从缓存中找到的用户数量

        # 只处理需要爬取的用户（新用户）
        new_users_to_process = {uid: all_users[uid] for uid in need_crawl_ids if uid in all_users}

        logger.info(f"从缓存中找到 {cached_users_count} 个已存在用户，需要处理 {len(new_users_to_process)} 个新用户")

        for user_id, user in tqdm(new_users_to_process.items(), desc="处理新用户"):
            try:
                # 检查是否已处理
                if self.state_manager.is_user_processed(str(user_id)):
                    skipped_count += 1
                    continue

                # 转换为 UserNew
                user_new = self.convert_user_to_user_new(user)
                if user_new is None:
                    failed_count += 1
                    continue

                # 保存到数据库
                if self.storage.save_user_new(user_new):
                    self.state_manager.mark_user_processed(str(user_id))
                    success_count += 1
                    logger.debug(f"成功保存用户 {user_id} 到数据库")
                else:
                    failed_count += 1

                # 定期保存进度
                if (success_count + failed_count) % 10 == 0:
                    self.state_manager.save_progress()

            except Exception as e:
                logger.error(f"处理用户 {user_id} 时发生异常: {e}")
                failed_count += 1
        
        # 最终保存进度
        self.state_manager.save_progress()
        
        results = {
            'total_users': len(all_users),
            'cached_users': cached_users_count,
            'new_users_processed': len(new_users_to_process),
            'success_count': success_count,
            'failed_count': failed_count,
            'skipped_count': skipped_count
        }
        
        logger.info(f"用户处理完成: {results}")
        return results
    
    def process_single_user(self, user_data: Dict) -> bool:
        """
        处理单个用户数据
        
        Args:
            user_data: 用户数据字典
            
        Returns:
            bool: 是否处理成功
        """
        try:
            # 解析用户数据
            user = self.processor.parse_user_from_dict(user_data)
            if not user:
                return False
            
            # 检查是否已处理
            if self.state_manager.is_user_processed(str(user.user_id)):
                return True
            
            # 转换为 UserNew
            user_new = self.convert_user_to_user_new(user)
            if user_new is None:
                return False
            
            # 保存到数据库
            if self.storage.save_user_new(user_new):
                self.state_manager.mark_user_processed(str(user.user_id))
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"处理单个用户时发生异常: {e}")
            return False
    
    def get_mid_cache_stats(self) -> Dict[str, int]:
        """获取 mid 缓存统计信息"""
        total = len(self.mid_cache)
        success = sum(1 for mid in self.mid_cache.values() if mid is not None)
        failed = total - success
        
        return {
            'total_cached': total,
            'success_cached': success,
            'failed_cached': failed
        }
    
    def clear_mid_cache(self):
        """清空 mid 缓存"""
        self.mid_cache.clear()
        logger.info("mid 缓存已清空")
