# 🚀 知识星球数据同步工具 - 使用指南

## 💡 核心设计理念

**默认行为**：下载 → 清洗(HTML转换) → 上传
**设计原则**：简单易用，默认开启所有功能，可选择性关闭

## 🎯 基本用法

### 主要命令 - sync

```bash
# 同步最近1小时数据（默认）
python main.py sync

# 同步不同时间范围
python main.py sync -r day        # 最近1天
python main.py sync -r week       # 最近1周  
python main.py sync -r month      # 最近1个月
python main.py sync -r year       # 最近1年
```

### 状态查看

```bash
# 查看文件和处理进度
python main.py status

# 重置处理进度
python main.py reset
```

## 🔧 高级选项

### 跳过某些步骤

```bash
# 只下载不上传（比如备份数据）
python main.py sync --skip-upload

# 只下载不清洗不上传（获取原始数据）
python main.py sync --skip-clean --skip-upload

# 跳过用户处理（只处理帖子）
python main.py sync --skip-users

# 使用已有文件，跳过下载
python main.py sync --skip-download

# 跳过图片下载
python main.py sync --skip-images
```

### 格式控制

```bash
# 不转换为HTML格式（默认会转换）
python main.py sync --no-html

# 指定特定日期前的数据
python main.py sync -r week -b 2025-06-15
```

## 🏢 服务器部署

### 定时任务设置

在服务器上设置 crontab 定时任务：

```bash
# 编辑定时任务
crontab -e

# 添加以下内容：

# 每小时同步一次
0 * * * * cd /path/to/project && python main.py sync

# 每天凌晨2点同步一天数据
0 2 * * * cd /path/to/project && python main.py sync -r day

# 每周一凌晨3点同步一周数据  
0 3 * * 1 cd /path/to/project && python main.py sync -r week

# 每月1号凌晨4点同步一个月数据
0 4 1 * * cd /path/to/project && python main.py sync -r month
```

### 日志管理

```bash
# 带日志的定时任务
0 * * * * cd /path/to/project && python main.py sync >> sync.log 2>&1
```

## 🎭 使用场景示例

### 日常维护

```bash
# 每天运行一次，获取最新数据
python main.py sync
```

### 数据备份

```bash
# 只下载不上传，用于备份
python main.py sync --skip-upload
```

### 数据修复

```bash
# 重新处理特定时间的数据
python main.py reset
python main.py sync -r week -b 2025-06-15
```

### 测试环境

```bash
# 获取原始数据用于测试
python main.py sync --skip-upload --no-html
```

## ⚡ 性能优化

### 默认优化

- **智能用户缓存**：避免重复处理用户信息
- **HTML转换**：默认启用，提升数据可读性
- **断点续传**：支持中断后继续处理

### 手动优化

- 定期运行 `python main.py reset` 清理进度状态
- 定期清理旧的数据文件节省空间

## 🔍 故障排除

### 常见问题

1. **配置错误**

   ```bash
   # 检查环境变量配置
   cat .env
   ```
2. **权限问题**

   ```bash
   # 检查文件权限
   ls -la data/
   ```
3. **进度卡住**

   ```bash
   # 重置进度
   python main.py reset
   ```
4. **查看状态**

   ```bash
   # 查看当前状态
   python main.py status
   ```

## 💎 最佳实践

1. **生产环境**：使用每小时同步 `python main.py sync`
2. **HTML格式**：保持默认开启，便于前端展示
3. **监控状态**：定期检查 `python main.py status`
4. **错误处理**：设置日志记录，便于排查问题
5. **数据清理**：定期清理旧文件，节省存储空间

## 🎁 命令速查

| 命令                                  | 说明                  |
| ------------------------------------- | --------------------- |
| `python main.py sync`               | 同步最近1小时（推荐） |
| `python main.py sync -r day`        | 同步最近1天           |
| `python main.py sync --no-html`     | 不转换HTML格式        |
| `python main.py sync --skip-upload` | 只下载不上传          |
| `python main.py status`             | 查看状态              |
| `python main.py reset`              | 重置进度              |

---

**🎉 现在你的数据同步工具变得超级简单易用了！默认就是最佳配置，直接 `python main.py sync` 就能搞定一切！**
