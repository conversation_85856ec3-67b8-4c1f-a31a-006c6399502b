# -*- coding: utf-8 -*-
"""
图片处理服务模块
负责图片的下载、处理、模糊化和Base64编码
"""

import io
import base64
import requests
from typing import Optional
from PIL import Image, ImageFilter

from config import config
from utils import get_logger

logger = get_logger(__name__)


class ImageProcessor:
    """图片处理服务"""
    
    def __init__(self):
        self.config = config.zsxq
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
        }
        
        # 模糊预览配置
        self.blur_width = 20  # 模糊预览的宽度
        self.blur_quality = 70  # JPEG质量
        self.blur_radius = 2  # 模糊半径
    
    def download_image(self, image_url: str) -> Optional[bytes]:
        """
        从URL下载图片数据
        
        Args:
            image_url: 图片URL
            
        Returns:
            bytes: 图片数据，失败返回None
        """
        if not image_url:
            return None
        
        for attempt in range(self.config.max_retries):
            try:
                response = requests.get(
                    image_url,
                    headers=self.headers,
                    timeout=self.config.request_timeout,
                    stream=True
                )
                response.raise_for_status()
                
                # 读取图片数据
                image_data = response.content
                logger.debug(f"成功下载图片: {image_url} ({len(image_data)} bytes)")
                return image_data
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"下载图片失败 (第 {attempt + 1}/{self.config.max_retries} 次): {e}")
                if attempt < self.config.max_retries - 1:
                    continue
                else:
                    logger.error(f"达到最大重试次数，放弃下载 {image_url}")
        
        return None
    
    def create_blur_preview(self, image_data: bytes) -> Optional[str]:
        """
        创建图片的模糊预览DataURL
        
        Args:
            image_data: 原始图片数据
            
        Returns:
            str: Base64编码的DataURL，失败返回None
        """
        try:
            # 打开图片
            with Image.open(io.BytesIO(image_data)) as img:
                # 转换为RGB模式（确保兼容性）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 计算缩放比例，保持宽高比
                original_width, original_height = img.size
                aspect_ratio = original_height / original_width
                new_height = int(self.blur_width * aspect_ratio)
                
                # 缩放图片
                img_resized = img.resize((self.blur_width, new_height), Image.Resampling.LANCZOS)
                
                # 应用模糊效果
                img_blurred = img_resized.filter(ImageFilter.GaussianBlur(radius=self.blur_radius))
                
                # 保存为JPEG格式的字节流
                output_buffer = io.BytesIO()
                img_blurred.save(
                    output_buffer, 
                    format='JPEG', 
                    quality=self.blur_quality,
                    optimize=True
                )
                
                # 编码为Base64
                img_bytes = output_buffer.getvalue()
                base64_string = base64.b64encode(img_bytes).decode('utf-8')
                
                # 创建DataURL
                data_url = f"data:image/jpeg;base64,{base64_string}"
                
                logger.debug(f"成功生成模糊预览: {len(data_url)} 字符")
                return data_url
                
        except Exception as e:
            logger.error(f"生成模糊预览失败: {e}")
            return None
    
    def process_image_with_blur(self, image_url: str) -> tuple[Optional[bytes], Optional[str]]:
        """
        处理图片：下载并生成模糊预览
        
        Args:
            image_url: 图片URL
            
        Returns:
            tuple: (原始图片数据, 模糊预览DataURL)
        """
        # 下载原始图片
        image_data = self.download_image(image_url)
        if not image_data:
            return None, None
        
        # 生成模糊预览
        blur_data_url = self.create_blur_preview(image_data)
        
        return image_data, blur_data_url
    
    def validate_image_format(self, image_data: bytes) -> bool:
        """
        验证图片格式是否有效
        
        Args:
            image_data: 图片数据
            
        Returns:
            bool: 是否为有效图片
        """
        try:
            with Image.open(io.BytesIO(image_data)) as img:
                # 尝试验证图片
                img.verify()
                return True
        except Exception:
            return False
    
    def get_image_info(self, image_data: bytes) -> Optional[dict]:
        """
        获取图片信息
        
        Args:
            image_data: 图片数据
            
        Returns:
            dict: 包含宽度、高度、格式等信息
        """
        try:
            with Image.open(io.BytesIO(image_data)) as img:
                return {
                    'width': img.width,
                    'height': img.height,
                    'format': img.format,
                    'mode': img.mode,
                    'size_bytes': len(image_data)
                }
        except Exception as e:
            logger.error(f"获取图片信息失败: {e}")
            return None
