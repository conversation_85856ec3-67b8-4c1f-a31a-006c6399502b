# -*- coding: utf-8 -*-
"""
用户缓存服务模块
负责管理用户数据的缓存，包括从数据库加载和动态更新
"""

from typing import Dict, Optional, Tuple
from supabase import create_client, Client

from config import config
from utils import get_logger
from services.member_crawler import MemberCrawler
from models import UserNew

logger = get_logger(__name__)


class UserCacheService:
    """用户缓存服务（单例模式）"""

    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(UserCacheService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        # 避免重复初始化
        if UserCacheService._initialized:
            return

        self.config = config.supabase
        self.client: Client = create_client(self.config.url, self.config.key)
        self.member_crawler = MemberCrawler()

        # 缓存映射
        self.uid_to_id: Dict[int, int] = {}  # user_id -> users.id
        self.mid_to_id: Dict[int, int] = {}  # zsxq_group_member_id -> users.id
        self.id_to_user: Dict[int, dict] = {}  # users.id -> 完整用户信息

        # 加载缓存
        self.load_cache_from_database()

        # 标记为已初始化
        UserCacheService._initialized = True
    
    def load_cache_from_database(self) -> bool:
        """
        从数据库加载用户缓存（支持分页查询获取所有数据）

        Returns:
            bool: 是否加载成功
        """
        try:
            logger.info("正在从数据库加载用户缓存...")

            # 清空现有缓存
            self.uid_to_id.clear()
            self.mid_to_id.clear()
            self.id_to_user.clear()

            # 分页查询所有用户数据
            page_size = 1000
            offset = 0
            total_loaded = 0

            while True:
                # 查询一页数据
                response = self.client.table('users').select(
                    'id, zsxq_group_member_id, user_id'

                ).range(offset, offset + page_size - 1).execute()

                users_data = response.data

                # 如果没有更多数据，退出循环
                if not users_data:
                    break

                # 构建缓存映射
                for user in users_data:
                    user_id = user['id']
                    uid = user.get('user_id')
                    mid = user.get('zsxq_group_member_id')

                    # 存储完整用户信息
                    self.id_to_user[user_id] = user

                    # 构建映射
                    if mid:
                        self.mid_to_id[mid] = user_id

                    if uid:
                        self.uid_to_id[uid] = user_id

                total_loaded += len(users_data)
                logger.info(f"已加载 {total_loaded} 个用户...")

                # 如果返回的数据少于页面大小，说明已经是最后一页
                if len(users_data) < page_size:
                    break

                # 准备下一页
                offset += page_size

            logger.info(f"用户缓存加载完成: {len(self.id_to_user)} 个用户")
            logger.info(f"  - UID映射: {len(self.uid_to_id)} 个")
            logger.info(f"  - MID映射: {len(self.mid_to_id)} 个")

            return True

        except Exception as e:
            logger.error(f"加载用户缓存失败: {e}")
            return False
    
    def get_user_id_by_uid(self, uid: int) -> Optional[int]:
        """
        通过 user_id (uid) 获取 users.id
        
        Args:
            uid: 知识星球用户ID
            
        Returns:
            int: users表的id，如果不存在返回None
        """
        return self.uid_to_id.get(uid)
    
    def get_user_id_by_mid(self, mid: int) -> Optional[int]:
        """
        通过 zsxq_group_member_id (mid) 获取 users.id
        
        Args:
            mid: 知识星球群组成员ID
            
        Returns:
            int: users表的id，如果不存在返回None
        """
        return self.mid_to_id.get(mid)
    
    def get_user_info_by_id(self, user_id: int) -> Optional[dict]:
        """
        通过 users.id 获取完整用户信息
        
        Args:
            user_id: users表的id
            
        Returns:
            dict: 用户信息，如果不存在返回None
        """
        return self.id_to_user.get(user_id)
    
    def update_user_uid_by_crawling(self, uid: int) -> Optional[int]:
        """
        通过爬取 mid 信息来更新现有用户的 uid

        Args:
            uid: 知识星球用户ID

        Returns:
            int: 更新的 users.id，如果失败返回None
        """
        try:
            logger.info(f"开始爬取用户 {uid} 的 mid 信息以更新现有记录...")

            # 获取用户的 mid 信息
            mid = self.member_crawler.get_member_mid(uid)
            if not mid:
                logger.warning(f"无法获取用户 {uid} 的 mid 信息")
                return None

            # 通过 mid 查找现有用户记录
            existing_user_id = self.get_user_id_by_mid(mid)
            if existing_user_id:
                logger.info(f"找到用户 mid {mid} 对应的记录 users.id={existing_user_id}，更新 user_id")
                # 更新现有记录的 user_id
                if self._update_user_uid(existing_user_id, uid):
                    logger.info(f"成功更新用户 {existing_user_id} 的 user_id 为 {uid}")
                    return existing_user_id
                else:
                    logger.warning(f"更新用户 {existing_user_id} 的 user_id 失败")
                    return existing_user_id  # 即使更新失败，也返回现有的 user_id
            else:
                logger.warning(f"未找到 mid {mid} 对应的用户记录，可能需要先导入用户数据")
                return None

        except Exception as e:
            logger.error(f"更新用户 {uid} 的 uid 失败: {e}")
            return None
    
    def _update_user_uid(self, user_id: int, uid: int) -> bool:
        """更新现有用户的 user_id"""
        try:
            self.client.table('users').update({'user_id': uid}).eq('id', user_id).execute()
            
            # 更新缓存
            if user_id in self.id_to_user:
                self.id_to_user[user_id]['user_id'] = uid
                self.uid_to_id[uid] = user_id
            
            return True
        except Exception as e:
            logger.error(f"更新用户 {user_id} 的 user_id 失败: {e}")
            return False
    
    def _save_new_user(self, user: UserNew) -> Optional[int]:
        """保存新用户到数据库并返回新的ID"""
        try:
            user_record = {
                'zsxq_group_member_id': user.zsxq_group_member_id,
                'user_id': user.user_id,
                'name': user.name,
                'alias': user.alias,
                'description': user.description,
                'join_time': user.join_time,
                'expiration_time': user.expiration_time
            }

            response = self.client.table('users').insert(user_record).execute()

            if response.data and len(response.data) > 0:
                return response.data[0]['id']

            return None

        except Exception as e:
            # 检查是否是重复键错误
            if 'duplicate key value violates unique constraint' in str(e):
                logger.info(f"用户 mid {user.zsxq_group_member_id} 已存在，尝试查找现有记录")
                # 尝试通过 mid 查找现有用户
                existing_user_id = self.get_user_id_by_mid(user.zsxq_group_member_id)
                if existing_user_id and user.user_id:
                    # 更新现有记录的 user_id
                    self._update_user_uid(existing_user_id, user.user_id)
                return existing_user_id
            else:
                logger.error(f"保存新用户失败: {e}")
                return None
    
    def _update_cache_with_new_user(self, user_id: int, user: UserNew):
        """用新用户信息更新内存缓存"""
        user_info = {
            'id': user_id,
            'zsxq_group_member_id': user.zsxq_group_member_id,
            'user_id': user.user_id,
            'name': user.name,
            'alias': user.alias,
            'avatar_path': user.avatar_path,
            'description': user.description,
            'join_time': user.join_time,
            'expiration_time': user.expiration_time,
            'phone_number': user.phone_number,
            'wechat_id': user.wechat_id,
            'profile_data': user.profile_data
        }

        self.id_to_user[user_id] = user_info

        if user.zsxq_group_member_id:
            self.mid_to_id[user.zsxq_group_member_id] = user_id

        if user.user_id:
            self.uid_to_id[user.user_id] = user_id
    
    def get_or_update_user_id(self, uid: int) -> Optional[int]:
        """
        获取或更新用户ID：先从缓存查找，如果不存在则标记为待处理

        Args:
            uid: 知识星球用户ID

        Returns:
            int: users表的id，如果不存在返回None（需要后续批量处理）
        """
        # 检查输入的UID是否合理（知识星球UID通常很长）
        if uid and len(str(uid)) < 10:
            logger.warning(f"收到异常短的UID: {uid}，这可能不是有效的知识星球用户ID")
            return None

        # 先从缓存查找
        user_id = self.get_user_id_by_uid(uid)
        if user_id:
            return user_id

        # 缓存中不存在，根据配置决定处理方式
        from config import config

        if config.app.batch_user_processing:
            # 批量处理模式：添加到待处理列表
            if not hasattr(self, '_pending_uids'):
                self._pending_uids = set()

            self._pending_uids.add(uid)
            logger.debug(f"用户 {uid} 不在缓存中，已添加到待处理列表")
            return None
        else:
            # 立即处理模式：直接爬取并更新
            logger.info(f"用户 {uid} 不在缓存中，开始立即爬取 mid 并更新现有记录...")
            return self.update_user_uid_by_crawling(uid)
    
    def process_pending_users(self) -> dict:
        """
        批量处理待处理的用户

        Returns:
            dict: 处理结果统计
        """
        if not hasattr(self, '_pending_uids') or not self._pending_uids:
            return {'processed': 0, 'failed': 0, 'skipped': 0}

        pending_uids = list(self._pending_uids)
        self._pending_uids.clear()

        logger.info(f"开始批量处理 {len(pending_uids)} 个待处理用户...")

        processed = 0
        failed = 0
        skipped = 0

        # 批量爬取用户信息
        for uid in pending_uids:
            try:
                # 再次检查缓存（可能在处理过程中已经被其他地方添加）
                if self.get_user_id_by_uid(uid):
                    skipped += 1
                    continue

                # 爬取并更新
                user_id = self.update_user_uid_by_crawling(uid)
                if user_id:
                    processed += 1
                else:
                    failed += 1

            except Exception as e:
                logger.error(f"处理用户 {uid} 时出错: {e}")
                failed += 1

        result = {
            'processed': processed,
            'failed': failed,
            'skipped': skipped,
            'total': len(pending_uids)
        }

        logger.info(f"批量处理完成: {result}")
        return result

    def get_pending_users_count(self) -> int:
        """获取待处理用户数量"""
        if not hasattr(self, '_pending_uids'):
            return 0
        return len(self._pending_uids)

    def refresh_cache(self) -> bool:
        """刷新缓存"""
        return self.load_cache_from_database()

    def get_cache_stats(self) -> dict:
        """获取缓存统计信息"""
        return {
            'total_users': len(self.id_to_user),
            'uid_mappings': len(self.uid_to_id),
            'mid_mappings': len(self.mid_to_id),
            'pending_users': self.get_pending_users_count()
        }
