# -*- coding: utf-8 -*-
"""
爬虫服务模块
负责从知识星球 API 爬取数据
"""

import json
import time
import requests
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any

from config import config
from utils import get_logger
from models import CrawlResult

logger = get_logger(__name__)


class ZsxqCrawler:
    """知识星球爬虫"""



    def __init__(self):
        self.config = config.zsxq
        self.headers = {
            # "Cookie": f"zsxq_access_token={self.config.cookie}",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
            "accept": "application/json, text/plain, */*",
            "accept-language": "en-US,en;q=0.9",
            "priority": "u=1, i",
            "sec-ch-ua": "\"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"macOS\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-site",
            "x-aduid": "46d57d18e-7c41-ee6c-fcec-b6c3dd7717d",
            "x-request-id": "b230a2466-f0ee-43e7-c8b1-7e10863b581",
            "x-signature": "6d3bc9a9bf9bcba9160c57680631d08f476ddd7f",
            "x-timestamp": "1750755540",
            "x-version": "2.76.0",
            "cookie": "sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2219768617aa2581-0c0e9bc6f87c2c-18525636-2073600-19768617aa3204d%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk3Njg2MTdhYTI1ODEtMGMwZTliYzZmODdjMmMtMTg1MjU2MzYtMjA3MzYwMC0xOTc2ODYxN2FhMzIwNGQifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%221976926dd8b773-07e93e93e93e94-18525636-2073600-1976926dd8d907%22%7D; zsxq_access_token=B34DA46E-7F3B-4798-9D24-5A67B461C6D2_CABE4A6553E9A3BF; abtest_env=product",
            "Referer": "https://wx.zsxq.com/",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        }
        self.base_url = f"https://api.zsxq.com/v2/groups/{self.config.group_id}/topics"
    
    def _make_request(self, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """发起 API 请求，包含重试机制"""
        for attempt in range(self.config.max_retries):
            try:
                logger.info(f"正在请求数据... (尝试 {attempt + 1}/{self.config.max_retries})")

                response = requests.get(
                    self.base_url, 
                    headers=self.headers, 
                    params=params, 
                    timeout=self.config.request_timeout
                )
                response.raise_for_status()
                
                data = response.json()
                if data.get("succeeded"):
                    logger.info("✅ API 调用成功！")
                    return data
                else:
                    error_info = data.get('info', '未知API逻辑错误')
                    logger.error(f"❌ API 返回逻辑错误: {error_info}")
                    
                    if 'login' in error_info.lower():
                        logger.error("错误信息提示登录问题，很可能是 Cookie 已过期")
                        return None
                    
                    raise Exception(f"API_LOGIC_ERROR: {error_info}")
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"❌ HTTP 请求失败: {e}")
            except json.JSONDecodeError as e:
                logger.error(f"❌ 解析 API 响应 JSON 失败: {e}")
            except Exception as e:
                logger.error(f"❌ 处理请求或 API 响应时发生错误: {e}")
            
            if attempt < self.config.max_retries - 1:
                logger.info(f"将在 {self.config.retry_wait_time} 秒后重试...")
                time.sleep(self.config.retry_wait_time)
            else:
                logger.error(f"已达到最大重试次数 ({self.config.max_retries} 次)")
        
        return None
    
    def crawl_topics(self, start_time: Optional[str] = None, end_time: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        爬取帖子数据

        Args:
            start_time: 开始时间（ISO 8601格式），爬虫会爬取该时间点之后的所有数据（时间范围的下限）
            end_time: 结束时间（ISO 8601格式），爬虫会从该时间点开始往前爬取（时间范围的上限）

        Returns:
            List[Dict]: 帖子数据列表
        """
        logger.info("开始爬取数据（无年份限制）")

        all_topics = []
        start_datetime = None
        end_datetime = None

        # 解析开始时间（时间范围下限）
        if start_time:
            try:
                start_datetime = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                logger.info(f"时间范围下限（开始时间）: {start_time}")
            except ValueError as e:
                logger.warning(f"开始时间格式解析失败: {e}，将不进行时间过滤")

        # 解析结束时间（时间范围上限）
        if end_time:
            try:
                end_datetime = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                logger.info(f"时间范围上限（结束时间）: {end_time}")
            except ValueError as e:
                logger.warning(f"结束时间格式解析失败: {e}，将从最新数据开始")

        if not start_time and not end_time:
            logger.info("将从最新的帖子开始爬取（无时间限制）")

        params = {"scope": "all", "count": self.config.count}
        # 如果指定了结束时间，设置为 API 的 end_time 参数
        if end_time:
            params["end_time"] = end_time

        while True:
            data = self._make_request(params)
            if not data:
                logger.error("请求失败，终止爬取")
                break

            topics = data.get("resp_data", {}).get("topics", [])
            if not topics:
                logger.info("✅ 已获取所有帖子，爬取结束")
                break

            # 处理这批数据
            valid_topics_in_batch = 0
            for topic in topics:
                should_include = True

                # 检查时间过滤
                if start_datetime:
                    topic_time_str = topic.get("create_time", "")
                    if topic_time_str:
                        try:
                            topic_datetime = datetime.fromisoformat(topic_time_str.replace('Z', '+00:00'))
                            if topic_datetime < start_datetime:
                                logger.debug(f"跳过早于开始时间的帖子 (帖子时间: {topic_time_str})")
                                should_include = False
                            else:
                                valid_topics_in_batch += 1
                        except ValueError:
                            logger.warning(f"时间解析失败，保留帖子: {topic_time_str}")
                            valid_topics_in_batch += 1
                    else:
                        # 没有时间信息，保留帖子
                        valid_topics_in_batch += 1
                else:
                    # 没有时间限制，保留所有帖子
                    valid_topics_in_batch += 1

                # 只添加符合条件的帖子
                if should_include:
                    all_topics.append(topic)

            # 如果这批数据中没有符合时间要求的帖子，说明已经超过了时间范围，停止爬取
            if start_datetime and valid_topics_in_batch == 0:
                logger.info(f"当前批次没有符合时间要求的帖子，停止爬取")
                break

            # 设置下一批次的起始时间
            end_time = topics[-1].get("create_time")
            if not end_time:
                logger.error("最后一个帖子缺少 'create_time'，无法确定下一批次的起始时间")
                break

            params["end_time"] = end_time
            logger.info(f"下一次请求将从 {end_time} 开始...")
            time.sleep(2)  # 请求间隔

        return all_topics
    
    def save_to_file(self, output_file: str, start_time: Optional[str] = None, end_time: Optional[str] = None) -> CrawlResult:
        """
        爬取数据并保存到文件

        Args:
            output_file: 输出文件路径
            start_time: 开始时间（时间范围下限）
            end_time: 结束时间（时间范围上限）

        Returns:
            CrawlResult: 爬取结果
        """
        try:
            # 确保输出目录存在
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            logger.info(f"--- 开始爬取帖子数据，将保存到 {output_file} ---")

            # 爬取所有数据
            all_topics = self.crawl_topics(start_time, end_time)
            total_count = len(all_topics)

            # 保存到文件
            with open(output_file, 'w', encoding='utf-8') as f:
                for i, topic in enumerate(all_topics, 1):
                    json_line = json.dumps(topic, ensure_ascii=False)
                    f.write(json_line + '\n')

                    if i % 10 == 0:
                        logger.info(f"已保存 {i} 条数据")
            
            logger.info(f"--- 爬取完毕，共保存了 {total_count} 条帖子到 {output_file} ---")
            
            return CrawlResult(
                success=True,
                total_count=total_count,
                file_path=output_file
            )
            
        except Exception as e:
            logger.error(f"爬取过程中发生错误: {e}")
            return CrawlResult(
                success=False,
                total_count=0,
                error_message=str(e)
            )
