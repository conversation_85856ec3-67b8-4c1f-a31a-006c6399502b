# -*- coding: utf-8 -*-
"""
成员信息爬虫服务模块
负责获取用户的 mid (zsxq_group_member_id) 信息
"""

import json
import time
import requests
from typing import Optional, Dict, Any
from urllib.parse import urljoin
from tqdm import tqdm

from config import config
from utils import get_logger

logger = get_logger(__name__)


class MemberCrawler:
    """知识星球成员信息爬虫"""
    
    def __init__(self):
        self.config = config.zsxq
        self.headers = {
            # "Cookie": f"zsxq_access_token={self.config.cookie}",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36",
            "accept": "application/json, text/plain, */*",
            "accept-language": "en-US,en;q=0.9",
            "priority": "u=1, i",
            "sec-ch-ua": "\"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"macOS\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-site",
            "x-aduid": "46d57d18e-7c41-ee6c-fcec-b6c3dd7717d",
            "x-request-id": "b230a2466-f0ee-43e7-c8b1-7e10863b581",
            "x-signature": "6d3bc9a9bf9bcba9160c57680631d08f476ddd7f",
            "x-timestamp": "1750755540",
            "x-version": "2.76.0",
            "cookie": "sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2219768617aa2581-0c0e9bc6f87c2c-18525636-2073600-19768617aa3204d%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk3Njg2MTdhYTI1ODEtMGMwZTliYzZmODdjMmMtMTg1MjU2MzYtMjA3MzYwMC0xOTc2ODYxN2FhMzIwNGQifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%221976926dd8b773-07e93e93e93e94-18525636-2073600-1976926dd8d907%22%7D; zsxq_access_token=B34DA46E-7F3B-4798-9D24-5A67B461C6D2_CABE4A6553E9A3BF; abtest_env=product",
            "Referer": "https://wx.zsxq.com/",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        }
        self.base_url = f"https://api.zsxq.com/v2/groups/{self.config.group_id}"
    
    def _make_request(self, url: str) -> Optional[Dict[str, Any]]:
        """发起 API 请求，包含重试机制"""
        for attempt in range(self.config.max_retries):
            try:
                logger.debug(f"正在请求: {url} (尝试 {attempt + 1}/{self.config.max_retries})")
                
                response = requests.get(
                    url, 
                    headers=self.headers, 
                    timeout=self.config.request_timeout
                )
                response.raise_for_status()
                
                data = response.json()
                if data.get("succeeded"):
                    logger.debug("✅ API 调用成功！")
                    return data
                else:
                    error_info = data.get('info', '未知API逻辑错误')
                    logger.error(f"❌ API 返回逻辑错误: {error_info}")
                    
                    if 'login' in error_info.lower():
                        logger.error("错误信息提示登录问题，很可能是 Cookie 已过期")
                        return None
                    
                    raise Exception(f"API_LOGIC_ERROR: {error_info}")
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"❌ HTTP 请求失败: {e}")
            except json.JSONDecodeError as e:
                logger.error(f"❌ 解析 API 响应 JSON 失败: {e}")
            except Exception as e:
                logger.error(f"❌ 处理请求或 API 响应时发生错误: {e}")
            
            if attempt < self.config.max_retries - 1:
                logger.debug(f"将在 {self.config.retry_wait_time} 秒后重试...")
                time.sleep(self.config.retry_wait_time)
            else:
                logger.error(f"已达到最大重试次数 ({self.config.max_retries} 次)")
        
        return None
    
    def get_member_summary(self, uid: int) -> Optional[Dict[str, Any]]:
        """
        获取指定用户的摘要信息，包含 mid (number)
        
        Args:
            uid: 用户 ID
            
        Returns:
            Dict: 用户摘要信息，包含 number 字段作为 mid
        """
        url = f"{self.base_url}/members/{uid}/summary"
        
        data = self._make_request(url)
        if not data:
            return None
        
        resp_data = data.get("resp_data", {})
        member_info = resp_data.get("member", {})
        
        if member_info:
            return member_info
        else:
            logger.warning(f"响应中没有找到用户 {uid} 的信息")
            return None
    
    def get_member_mid(self, uid: int) -> Optional[int]:
        """
        获取用户的 mid (zsxq_group_member_id)
        
        Args:
            uid: 用户 ID
            
        Returns:
            int: 用户的 mid，如果获取失败返回 None
        """
        member_info = self.get_member_summary(uid)
        if member_info:
            mid = member_info.get("number")
            if mid:
                logger.debug(f"成功获取用户 {uid} 的 mid: {mid}")
                return int(mid)
            else:
                logger.warning(f"用户 {uid} 的响应中没有 number 字段")
        
        logger.warning(f"无法获取用户 {uid} 的 mid")
        return None
    
    def get_member_full_info(self, uid: int) -> Optional[Dict[str, Any]]:
        """
        获取用户的完整成员信息
        
        Args:
            uid: 用户 ID
            
        Returns:
            Dict: 包含 mid 和其他成员信息的字典
        """
        member_info = self.get_member_summary(uid)
        if not member_info:
            return None
        
        # 提取关键信息
        result = {
            'user_id': member_info.get('user_id'),
            'mid': member_info.get('number'),
            'name': member_info.get('name'),
            'alias': member_info.get('alias'),
            'avatar_url': member_info.get('avatar_url'),
            'description': member_info.get('introduction', ''),
            'join_time': member_info.get('join_time'),
            'status': member_info.get('status'),
            'expiration_time': member_info.get('expiration_time'),
            'full_data': member_info  # 保留完整数据用于调试
        }
        
        logger.debug(f"成功获取用户 {uid} 的完整信息，MID: {result['mid']}")
        return result
    
    def batch_get_member_mids(self, user_ids: list, request_interval: float = 1.0) -> Dict[int, Optional[int]]:
        """
        批量获取用户的 mid 信息

        Args:
            user_ids: 用户 ID 列表
            request_interval: 请求间隔（秒）

        Returns:
            Dict: {user_id: mid} 的映射，如果获取失败则 mid 为 None
        """
        logger.info(f"--- 开始批量获取用户 mid 信息 ---")
        logger.info(f"总计需要处理 {len(user_ids)} 个用户")

        results = {}
        success_count = 0

        # 使用 tqdm 创建进度条
        with tqdm(
            total=len(user_ids),
            desc="获取用户MID",
            unit="用户",
            ncols=100,
            bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}] 成功:{postfix}"
        ) as pbar:

            for i, uid in enumerate(user_ids, 1):
                try:
                    mid = self.get_member_mid(uid)
                    results[uid] = mid

                    if mid is not None:
                        success_count += 1

                    # 更新进度条
                    pbar.set_postfix_str(f"{success_count}")
                    pbar.update(1)

                except Exception as e:
                    logger.error(f"处理用户 {uid} 时发生异常: {e}")
                    results[uid] = None
                    pbar.update(1)

                # 请求间隔控制
                if i < len(user_ids):
                    time.sleep(request_interval)

        logger.info(f"--- 批量获取完成 ---")
        logger.info(f"成功获取: {success_count}/{len(user_ids)} 个用户的MID")
        if len(user_ids) > 0:
            logger.info(f"成功率: {success_count/len(user_ids)*100:.1f}%")
        else:
            logger.info("成功率: N/A（无用户需要处理）")

        return results
