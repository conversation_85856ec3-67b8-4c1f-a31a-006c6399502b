# -*- coding: utf-8 -*-
"""
状态管理服务模块
负责处理进度状态的保存和加载
"""

import json
import os
from typing import Set, List
from datetime import datetime
from pathlib import Path

from config import config
from utils import get_logger
from models import ProcessState

logger = get_logger(__name__)


class StateManager:
    """状态管理器"""
    
    def __init__(self, state_file: str = None):
        self.state_file = state_file or config.app.state_file
        self.processed_user_ids: Set[str] = set()
        self.processed_topic_ids: Set[str] = set()
    
    def load_progress(self) -> ProcessState:
        """从状态文件加载已处理的用户和帖子ID"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                
                processed_users = state_data.get("processed_user_ids", [])
                processed_topics = state_data.get("processed_topic_ids", [])
                last_update = state_data.get("last_update")
                
                self.processed_user_ids.update(processed_users)
                self.processed_topic_ids.update(processed_topics)
                
                logger.info(f"已加载处理过的用户数: {len(self.processed_user_ids)}")
                logger.info(f"已加载处理过的帖子数: {len(self.processed_topic_ids)}")
                
                return ProcessState(
                    processed_user_ids=processed_users,
                    processed_topic_ids=processed_topics,
                    last_update=last_update
                )
                
            except (json.JSONDecodeError, IOError) as e:
                logger.warning(f"状态文件 {self.state_file} 加载失败 ({e})，将重新开始")
                return ProcessState(processed_user_ids=[], processed_topic_ids=[])
        
        logger.info("状态文件不存在，将从头开始处理")
        return ProcessState(processed_user_ids=[], processed_topic_ids=[])
    
    def save_progress(self) -> bool:
        """将当前处理进度保存到状态文件"""
        try:
            state_data = {
                "processed_user_ids": list(self.processed_user_ids),
                "processed_topic_ids": list(self.processed_topic_ids),
                "last_update": datetime.now().isoformat()
            }

            # 确保目录存在
            state_file_path = Path(self.state_file)
            if state_file_path.parent != Path('.'):  # 如果不是当前目录
                state_file_path.parent.mkdir(parents=True, exist_ok=True)

            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=4, ensure_ascii=False)

            logger.debug(f"状态已保存到 {self.state_file}")
            return True

        except IOError as e:
            logger.error(f"保存状态到 {self.state_file} 失败: {e}")
            return False
    
    def is_user_processed(self, user_id: str) -> bool:
        """检查用户是否已处理"""
        return user_id in self.processed_user_ids
    
    def is_topic_processed(self, topic_id: str) -> bool:
        """检查帖子是否已处理"""
        return topic_id in self.processed_topic_ids
    
    def mark_user_processed(self, user_id: str):
        """标记用户为已处理"""
        self.processed_user_ids.add(user_id)
    
    def mark_topic_processed(self, topic_id: str):
        """标记帖子为已处理"""
        self.processed_topic_ids.add(topic_id)
    
    def get_processed_counts(self) -> tuple[int, int]:
        """获取已处理的用户和帖子数量"""
        return len(self.processed_user_ids), len(self.processed_topic_ids)
    
    def reset_progress(self):
        """重置处理进度"""
        self.processed_user_ids.clear()
        self.processed_topic_ids.clear()
        
        if os.path.exists(self.state_file):
            try:
                os.remove(self.state_file)
                logger.info(f"已删除状态文件: {self.state_file}")
            except OSError as e:
                logger.error(f"删除状态文件失败: {e}")
        
        logger.info("处理进度已重置")
