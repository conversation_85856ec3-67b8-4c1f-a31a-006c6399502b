# -*- coding: utf-8 -*-
"""
内容清洗服务
负责清洗和转换知识星球的原始内容数据
"""

import re
import html
import json
from typing import Dict, Any, List, Optional, Tuple
from urllib.parse import unquote
from bs4 import BeautifulSoup
from utils import get_logger

logger = get_logger(__name__)


class ContentCleaner:
    """内容清洗器"""
    
    def __init__(self):
        # 编译正则表达式以提高性能
        self.patterns = {
            # 移除多余空白字符
            'whitespace': re.compile(r'\s+'),
            
            # 提取标题
            'title': re.compile(r'^<e\s+type="text_bold"\s+title="([^"]+)"\s*/>$'),
            
            # 提取标签
            'hashtag': re.compile(r'<e\s+type="hashtag"[^>]*?\s+title="([^"]+)"\s*/>'),
            
            # 提取链接
            'link': re.compile(r'<e\s+type="web_url"[^>]*?\s+title="([^"]+)"\s*/>'),
            
            # 提取用户提及
            'mention': re.compile(r'<e\s+type="mention"[^>]*?\s+title="([^"]+)"\s*/>'),
            
            # 移除HTML标签
            'html_tags': re.compile(r'<[^>]+>'),
            
            # 清理换行符
            'newlines': re.compile(r'\n{3,}'),
            
            # 清理特殊字符
            'special_chars': re.compile(r'[\u200b\u200c\u200d\ufeff]'),  # 零宽字符
        }
    
    def clean_text_content(self, text: str) -> str:
        """
        清洗文本内容（用于普通文本）
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清洗后的文本
        """
        if not text:
            return ""
        
        # 1. HTML解码
        text = html.unescape(text)
        
        # 2. 移除零宽字符
        text = self.patterns['special_chars'].sub('', text)
        
        # 3. 规范化空白字符
        text = self.patterns['whitespace'].sub(' ', text)
        
        # 4. 清理多余的换行符
        text = self.patterns['newlines'].sub('\n\n', text)
        
        # 5. 移除首尾空白
        text = text.strip()
        
        return text

    def clean_text_for_html(self, text: str) -> str:
        """
        清洗文本内容（用于HTML转换，保留换行符）
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清洗后的文本（保留换行符）
        """
        if not text:
            return ""
        
        # 1. HTML解码
        text = html.unescape(text)
        
        # 2. 移除零宽字符
        text = self.patterns['special_chars'].sub('', text)
        
        # 3. 标准化换行符
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        
        # 4. 清理连续的空格但保留换行符
        # 将多个空格合并为一个，但不影响换行符
        lines = text.split('\n')
        cleaned_lines = []
        for line in lines:
            # 对每行单独处理空格
            cleaned_line = re.sub(r'[ \t]+', ' ', line).strip()
            cleaned_lines.append(cleaned_line)
        
        text = '\n'.join(cleaned_lines)
        
        # 5. 清理多余的连续换行符（3个或以上合并为2个）
        text = self.patterns['newlines'].sub('\n\n', text)
        
        # 6. 移除首尾空白
        text = text.strip()
        
        return text

    def convert_to_html(self, text: str) -> str:
        """
        将清洗后的文本转换为HTML格式
        按换行符分段，每个段落转换为独立的<p>标签

        Args:
            text: 清洗后的文本

        Returns:
            str: HTML格式的文本
        """
        if not text:
            return ""

        # 步骤 1: 标准化所有换行符为 \n
        normalized_text = text.replace('\r\n', '\n').replace('\r', '\n')

        # 步骤 2: 按换行符分割成段落，并过滤空段落
        lines = normalized_text.split('\n')
        paragraphs = [line.strip() for line in lines if line.strip()]

        if not paragraphs:
            return ""

        processed_paragraphs = []

        # 步骤 3: 遍历每个段落，进行内部处理
        for p_text in paragraphs:
            # 使用 BeautifulSoup 解析每个段落，安全地处理标签
            soup = BeautifulSoup(p_text, 'html.parser')

            # 查找所有自定义的 <e> 标签
            for tag in soup.find_all('e'):
                tag_type = tag.get('type')
                title_encoded = tag.get('title', '')

                try:
                    title_decoded = unquote(title_encoded)

                    if tag_type == 'web' or tag_type == 'web_url':
                        # 处理链接标签
                        href_encoded = tag.get('href', '') or tag.get('url', '')
                        href_decoded = unquote(href_encoded) if href_encoded else title_decoded
                        new_a_tag = soup.new_tag('a', href=href_decoded, target='_blank', rel='noopener noreferrer')
                        new_a_tag.string = title_decoded
                        tag.replace_with(new_a_tag)

                    elif tag_type == 'text_bold':
                        # 处理粗体标签
                        new_strong_tag = soup.new_tag('strong')
                        new_strong_tag.string = title_decoded
                        tag.replace_with(new_strong_tag)

                    elif tag_type == 'hashtag':
                        # 处理标签，转换为带样式的span
                        new_span_tag = soup.new_tag('span', **{'class': 'hashtag'})
                        new_span_tag.string = f"#{title_decoded}"
                        tag.replace_with(new_span_tag)

                    elif tag_type == 'mention':
                        # 处理用户提及，转换为带样式的span
                        new_span_tag = soup.new_tag('span', **{'class': 'mention'})
                        new_span_tag.string = f"@{title_decoded}"
                        tag.replace_with(new_span_tag)

                    else:
                        # 其他类型的标签，移除但保留文本内容
                        tag.replace_with(title_decoded)

                except Exception as e:
                    logger.warning(f"处理标签时发生错误: {tag}, 错误: {e}")
                    # 出错时移除标签但尝试保留文本
                    tag.replace_with(title_decoded if title_decoded else "")

            # 将处理完 <e> 标签的段落内容转换回字符串
            processed_text = soup.decode_contents()

            # 步骤 4: 用 <p> 标签包裹每个处理后的段落
            processed_paragraphs.append(f'<p>{processed_text}</p>')

        # 步骤 5: 将所有处理好的段落 HTML 连接成一个完整的字符串
        return "".join(processed_paragraphs)

    def extract_title_from_content(self, content: str) -> Tuple[Optional[str], str]:
        """
        从内容中提取标题
        
        Args:
            content: 原始内容
            
        Returns:
            Tuple[title, remaining_content]: 标题和剩余内容
        """
        if not content:
            return None, ""
        
        lines = content.split('\n')
        if not lines:
            return None, content
        
        # 检查第一行是否是标题格式
        title_match = self.patterns['title'].match(lines[0].strip())
        if title_match:
            title = unquote(title_match.group(1))
            remaining_content = '\n'.join(lines[1:])
            return title, remaining_content
        
        return None, content
    
    def extract_hashtags(self, content: str) -> Tuple[List[str], str]:
        """
        提取标签并移除标签标记
        
        Args:
            content: 原始内容
            
        Returns:
            Tuple[tags, cleaned_content]: 标签列表和清理后的内容
        """
        if not content:
            return [], ""
        
        # 提取所有标签
        tag_matches = self.patterns['hashtag'].findall(content)
        tags = [unquote(tag).strip('#') for tag in tag_matches]
        
        # 移除标签标记
        cleaned_content = self.patterns['hashtag'].sub('', content)
        
        return tags, cleaned_content
    
    def extract_links(self, content: str) -> Tuple[List[str], str]:
        """
        提取链接并转换为可读格式
        
        Args:
            content: 原始内容
            
        Returns:
            Tuple[links, cleaned_content]: 链接列表和清理后的内容
        """
        if not content:
            return [], ""
        
        # 提取所有链接
        link_matches = self.patterns['link'].findall(content)
        links = [unquote(link) for link in link_matches]
        
        # 将链接标记替换为可读文本
        def replace_link(match):
            title = unquote(match.group(1))
            return f"[链接: {title}]"
        
        cleaned_content = self.patterns['link'].sub(replace_link, content)
        
        return links, cleaned_content
    
    def extract_mentions(self, content: str) -> Tuple[List[str], str]:
        """
        提取用户提及并转换为可读格式
        
        Args:
            content: 原始内容
            
        Returns:
            Tuple[mentions, cleaned_content]: 提及列表和清理后的内容
        """
        if not content:
            return [], ""
        
        # 提取所有用户提及
        mention_matches = self.patterns['mention'].findall(content)
        mentions = [unquote(mention) for mention in mention_matches]
        
        # 将提及标记替换为可读文本
        def replace_mention(match):
            name = unquote(match.group(1))
            return f"@{name}"
        
        cleaned_content = self.patterns['mention'].sub(replace_mention, content)
        
        return mentions, cleaned_content
    
    def clean_topic_content(self, raw_content: str, convert_to_html: bool = False) -> Dict[str, Any]:
        """
        完整清洗帖子内容

        Args:
            raw_content: 原始帖子内容
            convert_to_html: 是否转换为HTML格式

        Returns:
            Dict: 清洗后的内容数据
        """
        if not raw_content:
            return {
                'title': None,
                'content': "",
                'tags': [],
                'links': [],
                'mentions': [],
                'raw_content': raw_content
            }
        
        content = raw_content
        
        # 1. 提取标题
        title, content = self.extract_title_from_content(content)
        
        # 2. 提取标签
        tags, content = self.extract_hashtags(content)
        
        # 3. 提取链接
        links, content = self.extract_links(content)
        
        # 4. 提取用户提及
        mentions, content = self.extract_mentions(content)
        
        # 5. 最终清洗 - 根据是否转换HTML使用不同的清洗方法
        if convert_to_html:
            # 为HTML转换保留换行符
            cleaned_content = self.clean_text_for_html(content)
            html_content = self.convert_to_html(cleaned_content)
        else:
            # 普通文本清洗
            cleaned_content = self.clean_text_content(content)
            html_content = None

        return {
            'title': title,
            'content': cleaned_content,
            'html_content': html_content,
            'tags': tags,
            'links': links,
            'mentions': mentions,
            'raw_content': raw_content
        }
    
    def clean_topic_data(self, topic_data: Dict[str, Any], convert_to_html: bool = False) -> Dict[str, Any]:
        """
        清洗完整的帖子数据

        Args:
            topic_data: 原始帖子数据
            convert_to_html: 是否转换为HTML格式

        Returns:
            Dict: 清洗后的帖子数据
        """
        if not topic_data:
            return topic_data
        
        # 复制原始数据
        cleaned_data = topic_data.copy()
        
        # 获取帖子类型和原始文本
        topic_type = topic_data.get('type')
        if topic_type and topic_type in topic_data:
            raw_text = topic_data[topic_type].get('text', '')
            
            # 清洗内容
            cleaned_content = self.clean_topic_content(raw_text, convert_to_html)

            # 更新数据
            if topic_type not in cleaned_data:
                cleaned_data[topic_type] = {}

            cleaned_data[topic_type]['text'] = cleaned_content['content']
            cleaned_data['title'] = cleaned_content['title']
            cleaned_data['tags'] = cleaned_content['tags']
            cleaned_data['links'] = cleaned_content['links']
            cleaned_data['mentions'] = cleaned_content['mentions']
            cleaned_data['raw_text'] = raw_text

            # 如果转换了HTML，也保存HTML内容
            if cleaned_content['html_content']:
                cleaned_data[topic_type]['html_text'] = cleaned_content['html_content']
        
        return cleaned_data
    
    def clean_jsonl_file(self, input_file: str, output_file: str, convert_to_html: bool = False) -> Dict[str, int]:
        """
        清洗JSONL文件中的所有帖子数据

        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
            convert_to_html: 是否转换为HTML格式

        Returns:
            Dict: 处理统计信息
        """
        stats = {
            'total_topics': 0,
            'cleaned_topics': 0,
            'errors': 0
        }
        
        try:
            with open(input_file, 'r', encoding='utf-8') as infile, \
                 open(output_file, 'w', encoding='utf-8') as outfile:
                
                for line_num, line in enumerate(infile, 1):
                    try:
                        # 解析JSON
                        topic_data = json.loads(line.strip())
                        stats['total_topics'] += 1
                        
                        # 清洗数据
                        cleaned_data = self.clean_topic_data(topic_data, convert_to_html)
                        stats['cleaned_topics'] += 1
                        
                        # 写入清洗后的数据
                        outfile.write(json.dumps(cleaned_data, ensure_ascii=False) + '\n')
                        
                    except json.JSONDecodeError as e:
                        logger.error(f"第{line_num}行JSON解析失败: {e}")
                        stats['errors'] += 1
                    except Exception as e:
                        logger.error(f"第{line_num}行处理失败: {e}")
                        stats['errors'] += 1
                        
        except Exception as e:
            logger.error(f"文件处理失败: {e}")
            raise
        
        logger.info(f"清洗完成: 总计{stats['total_topics']}条，成功{stats['cleaned_topics']}条，错误{stats['errors']}条")
        return stats
