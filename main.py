# -*- coding: utf-8 -*-
"""
知识星球数据同步工具 - 简化版
默认行为：下载 -> 清洗(HTML) -> 上传
"""

import argparse
import sys
from pathlib import Path
from datetime import datetime, timedelta, timezone

from config import config
from utils import get_logger
from workflows import DownloadWorkflow, UploadWorkflow, CleanWorkflow

logger = get_logger(__name__)


def get_time_range(range_type, before_date=None):
    """获取预定义的时间范围"""
    beijing_tz = timezone(timedelta(hours=8))

    if before_date:
        try:
            base_date = datetime.strptime(before_date, '%Y-%m-%d')
            base_datetime = base_date.replace(hour=23, minute=59, second=59, tzinfo=beijing_tz)
        except ValueError:
            raise ValueError(f"无效的日期格式: {before_date}，请使用 YYYY-MM-DD 格式")
    else:
        base_datetime = datetime.now(beijing_tz)

    if range_type == 'hour':
        start_time = base_datetime - timedelta(hours=1)
    elif range_type == 'day':
        start_time = (base_datetime - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
    elif range_type == 'week':
        start_time = (base_datetime - timedelta(days=7)).replace(hour=0, minute=0, second=0, microsecond=0)
    elif range_type == 'month':
        start_time = (base_datetime - timedelta(days=30)).replace(hour=0, minute=0, second=0, microsecond=0)
    elif range_type == 'year':
        start_time = (base_datetime - timedelta(days=365)).replace(hour=0, minute=0, second=0, microsecond=0)
    else:
        return None, None

    end_time = base_datetime if before_date else None
    return (
        start_time.strftime('%Y-%m-%dT%H:%M:%S.000+0800'),
        end_time.strftime('%Y-%m-%dT%H:%M:%S.000+0800') if end_time else None
    )


def sync_command(args):
    """一键同步：下载 -> 清洗 -> 上传"""
    print("🚀 开始数据同步...")
    
    # 参数处理
    range_type = args.range
    convert_to_html = not args.no_html  # 默认转换为HTML
    
    print(f"📅 时间范围: {range_type}")
    print(f"🔧 HTML转换: {'开启' if convert_to_html else '关闭'}")
    print(f"🖼️ 图片处理: {'跳过' if args.skip_images else '开启'}")
    
    try:
        # 第1步：下载数据
        if not args.skip_download:
            print("\n=== 第1步: 下载数据 ===")
            download_result = download_data(range_type, args.before_date, args.output)
            if not download_result:
                return 1
            download_file = download_result
        else:
            # 使用最新文件
            download_workflow = DownloadWorkflow()
            download_file = download_workflow.get_latest_data_file()
            if not download_file:
                print("❌ 没有找到数据文件")
                return 1
            print(f"使用已有文件: {download_file}")
        
        # 第2步：清洗数据  
        if not args.skip_clean:
            print("\n=== 第2步: 清洗数据 ===")
            cleaned_file = clean_data(download_file, convert_to_html)
            if not cleaned_file:
                print("⚠️ 清洗失败，使用原始文件")
                cleaned_file = download_file
        else:
            cleaned_file = download_file
            print("\n=== 跳过数据清洗 ===")
        
        # 第3步：上传数据
        if not args.skip_upload:
            print("\n=== 第3步: 上传数据 ===")
            upload_result = upload_data(cleaned_file, args.skip_users, args.skip_group, args.skip_images)
            if not upload_result:
                return 1
        else:
            print(f"\n=== 跳过数据上传 ===")
            print(f"已处理文件: {cleaned_file}")
        
        print("\n🎉 同步完成！")
        return 0
        
    except Exception as e:
        print(f"❌ 同步失败: {e}")
        return 1


def download_data(range_type, before_date=None, output_file=None):
    """下载数据"""
    workflow = DownloadWorkflow()
    
    # 获取时间范围
    start_time, end_time = get_time_range(range_type, before_date)
    if start_time:
        if before_date:
            print(f"时间范围: {before_date} 之前的 {range_type}")
        else:
            print(f"时间范围: 最近 {range_type}")
    
    # 执行下载
    result = workflow.download_data(
        output_file=output_file,
        start_time=start_time, 
        end_time=end_time
    )
    
    if result.success:
        print(f"✅ 下载成功: {result.total_count} 条数据")
        return result.file_path
    else:
        print(f"❌ 下载失败: {result.error_message}")
        return None


def clean_data(input_file, convert_to_html=True):
    """清洗数据"""
    workflow = CleanWorkflow()
    
    try:
        result = workflow.clean_file(input_file, None, convert_to_html)
        print(f"✅ 清洗完成: {result['cleaned_topics']}/{result['total_topics']} 条")
        print(f"HTML转换: {'是' if convert_to_html else '否'}")
        return result['output_file']
    except Exception as e:
        print(f"❌ 清洗失败: {e}")
        return None


def upload_data(file_path, skip_users=False, skip_group=False, skip_images=False):
    """上传数据"""
    workflow = UploadWorkflow()
    
    try:
        results = workflow.upload_data(
            file_path=file_path,
            skip_users=skip_users,
            skip_group=skip_group,
            skip_images=skip_images
        )
        print("✅ 上传成功!")
        print(f"处理结果: 用户 {results['users_processed']}, "
              f"星球 {results['group_processed']}, "
              f"帖子 {results['topics_processed']}")
        return True
    except Exception as e:
        print(f"❌ 上传失败: {e}")
        return False


def status_command(args):
    """查看状态"""
    # 查看数据文件
    download_workflow = DownloadWorkflow()
    files = download_workflow.list_data_files()
    
    print("📂 数据文件:")
    if not files:
        print("  (无)")
    else:
        for i, file in enumerate(files[:5], 1):  # 只显示最近5个
            file_path = Path(config.app.data_dir) / file
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"  {i}. {file} ({size_mb:.1f}MB)")
    
    # 查看上传进度  
    upload_workflow = UploadWorkflow()
    progress = upload_workflow.get_progress_info()
    print(f"\n📊 上传进度:")
    print(f"  已处理用户: {progress['processed_users']}")
    print(f"  已处理帖子: {progress['processed_topics']}")


def reset_command(args):
    """重置进度"""
    upload_workflow = UploadWorkflow()
    upload_workflow.reset_progress()
    print("✅ 处理进度已重置")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="知识星球数据同步工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
常用命令:
  python main.py sync                    # 同步最近1小时 (默认)
  python main.py sync -r day             # 同步最近1天
  python main.py sync -r week            # 同步最近1周  
  python main.py sync -r month           # 同步最近1个月
  
  python main.py sync --no-html          # 不转换为HTML格式
  python main.py sync --skip-upload      # 只下载不上传
  python main.py sync --skip-clean       # 跳过清洗
  python main.py sync --skip-images      # 跳过图片处理和上传
  
  python main.py status                  # 查看状态
  python main.py reset                   # 重置进度
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='命令')
    
    # sync 命令 (主要命令)
    sync_parser = subparsers.add_parser('sync', help='数据同步 (推荐)')
    sync_parser.add_argument('-r', '--range', 
                           choices=['hour', 'day', 'week', 'month', 'year'],
                           default='hour', help='时间范围 (默认: hour)')
    sync_parser.add_argument('-b', '--before-date', help='基准日期 (YYYY-MM-DD)')
    sync_parser.add_argument('-o', '--output', help='输出文件名')
    
    # 流程控制参数
    sync_parser.add_argument('--skip-download', action='store_true', help='跳过下载')
    sync_parser.add_argument('--skip-clean', action='store_true', help='跳过清洗') 
    sync_parser.add_argument('--skip-upload', action='store_true', help='跳过上传')
    sync_parser.add_argument('--skip-users', action='store_true', help='跳过用户处理')
    sync_parser.add_argument('--skip-group', action='store_true', help='跳过星球处理')
    sync_parser.add_argument('--skip-images', action='store_true', help='跳过图片处理')
    
    # 格式控制参数
    sync_parser.add_argument('--no-html', action='store_true', 
                           help='不转换为HTML格式 (默认转换)')
    
    sync_parser.set_defaults(func=sync_command)
    
    # status 命令
    status_parser = subparsers.add_parser('status', help='查看状态')
    status_parser.set_defaults(func=status_command)
    
    # reset 命令
    reset_parser = subparsers.add_parser('reset', help='重置进度')
    reset_parser.set_defaults(func=reset_command)
    
    # 解析参数
    args = parser.parse_args()
    
    if not args.command:
        print("🚀 知识星球数据同步工具")
        print("\n💡 推荐用法:")
        print("   python main.py sync                 # 同步最近1小时")
        print("   python main.py sync -r day          # 同步最近1天")
        print("   python main.py sync -r week         # 同步最近1周")
        print("   python main.py sync -r month        # 同步最近1个月")
        print("\n🔧 其他命令:")
        print("   python main.py status               # 查看状态")
        print("   python main.py reset                # 重置进度")
        print("\n使用 --help 查看详细帮助")
        return 1
    
    # 执行命令
    try:
        return args.func(args)
    except KeyboardInterrupt:
        print("\n操作被中断")
        return 1
    except Exception as e:
        logger.error(f"执行出错: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
