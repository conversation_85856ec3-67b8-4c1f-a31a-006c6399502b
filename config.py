# -*- coding: utf-8 -*-
"""
配置管理模块
统一管理所有配置项，支持环境变量和配置文件
"""

import os
from dataclasses import dataclass
from typing import Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


@dataclass
class ZsxqConfig:
    """知识星球爬虫配置"""
    cookie: str
    group_id: str
    count: int = 20
    max_retries: int = 5
    retry_wait_time: int = 10
    request_timeout: int = 15
    
    @classmethod
    def from_env(cls) -> 'ZsxqConfig':
        return cls(
            cookie=os.getenv('ZSXQ_COOKIE', ''),
            group_id=os.getenv('ZSXQ_GROUP_ID', '48415154841458'),
            count=int(os.getenv('ZSXQ_COUNT', '20')),
            max_retries=int(os.getenv('ZSXQ_MAX_RETRIES', '5')),
            retry_wait_time=int(os.getenv('ZSXQ_RETRY_WAIT_TIME', '10')),
            request_timeout=int(os.getenv('ZSXQ_REQUEST_TIMEOUT', '15'))
        )


@dataclass
class SupabaseConfig:
    """Supabase 配置"""
    url: str
    key: str
    avatars_bucket: str = "avatars"
    group_backgrounds_bucket: str = "group-backgrounds"
    topic_images_bucket: str = "topic-images"
    
    @classmethod
    def from_env(cls) -> 'SupabaseConfig':
        url = os.getenv('SUPABASE_URL')
        key = os.getenv('SUPABASE_KEY')
        
        if not url or not key:
            raise ValueError("请确保环境变量中已配置 SUPABASE_URL 和 SUPABASE_KEY")
        
        return cls(
            url=url,
            key=key,
            avatars_bucket=os.getenv('SUPABASE_AVATARS_BUCKET', 'avatars'),
            group_backgrounds_bucket=os.getenv('SUPABASE_GROUP_BACKGROUNDS_BUCKET', 'group-backgrounds'),
            topic_images_bucket=os.getenv('SUPABASE_TOPIC_IMAGES_BUCKET', 'topic-images')
        )


@dataclass
class AppConfig:
    """应用配置"""
    data_dir: str = "data"
    state_file: str = "processed_state.json"
    log_level: str = "INFO"
    batch_user_processing: bool = True  # 是否启用批量用户处理

    @classmethod
    def from_env(cls) -> 'AppConfig':
        return cls(
            data_dir=os.getenv('DATA_DIR', 'data'),
            state_file=os.getenv('STATE_FILE', 'processed_state.json'),
            log_level=os.getenv('LOG_LEVEL', 'INFO'),
            batch_user_processing=os.getenv('BATCH_USER_PROCESSING', 'true').lower() == 'true'
        )


class Config:
    """统一配置管理器"""
    
    def __init__(self):
        self.zsxq = ZsxqConfig.from_env()
        self.supabase = SupabaseConfig.from_env()
        self.app = AppConfig.from_env()
    
    def validate(self) -> bool:
        """验证配置是否完整"""
        if not self.zsxq.cookie:
            print("警告: ZSXQ_COOKIE 未配置")
            return False
        
        if not self.zsxq.group_id:
            print("警告: ZSXQ_GROUP_ID 未配置")
            return False
        
        return True


# 全局配置实例
config = Config()
