# 服务器部署指南

## 🚀 快速部署

### 1. 准备服务器环境

```bash
# 连接到服务器
ssh ubuntu@**************

# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要的依赖
sudo apt install -y python3 python3-pip python3-venv git curl
```

### 2. 上传项目文件

```bash
# 在本地打包项目（排除不必要的文件）
tar --exclude='.git' --exclude='__pycache__' --exclude='*.pyc' --exclude='.venv' --exclude='data' -czf zsxq-tool.tar.gz .

# 上传到服务器
scp zsxq-tool.tar.gz ubuntu@**************:~/

# 在服务器上解压
ssh ubuntu@**************
cd ~
tar -xzf zsxq-tool.tar.gz
mv PythonProject zsxq-tool  # 如果需要重命名
```

### 3. 自动部署

```bash
# 进入项目目录
cd ~/zsxq-tool

# 运行自动部署脚本
sudo ./scripts/deploy.sh

# 或者指定自定义配置
sudo ./scripts/deploy.sh -u zsxq -d /opt/zsxq-tool
```

### 4. 配置环境变量

```bash
# 编辑生产环境配置
sudo -u zsxq vim /opt/zsxq-tool/.env

# 配置内容：
ZSXQ_COOKIE=你的知识星球Cookie
ZSXQ_GROUP_ID=48415154841458
SUPABASE_URL=你的Supabase项目URL
SUPABASE_KEY=你的Supabase密钥

# 性能优化配置
BATCH_USER_PROCESSING=true
LOG_LEVEL=INFO
```

## 📋 定时任务说明

自动部署后会配置以下定时任务：

```bash
# 每小时同步（过去1小时的新内容）- 包含数据清洗
0 * * * * python main.py sync -r hour --skip-users --skip-group

# 每天凌晨2点同步（今天所有内容）- 包含数据清洗  
0 2 * * * python main.py sync -r day --skip-group

# 每周日凌晨3点同步（过去一周所有内容）- 包含数据清洗
0 3 * * 0 python main.py sync -r week

# 每月1号凌晨4点同步（过去一个月所有内容）- 包含数据清洗
0 4 1 * * python main.py sync -r month

# 每天凌晨5点执行备份
0 5 * * * ./scripts/backup.sh

# 每2小时执行健康检查
0 */2 * * * python scripts/health_check.py

# 自动清理任务
0 1 * * * find logs/ -name "*.log" -mtime +30 -delete
0 1 * * 1 find data/ -name "zsxq_topics_*.jsonl" -mtime +7 -delete
```

## 🔧 手动管理

### 查看和管理定时任务

```bash
# 查看当前定时任务
sudo -u zsxq crontab -l

# 编辑定时任务
sudo -u zsxq crontab -e

# 移除所有定时任务
sudo -u zsxq crontab -r
```

### 手动执行同步

```bash
# 切换到部署用户
sudo -u zsxq -i

# 进入项目目录
cd /opt/zsxq-tool

# 激活虚拟环境
source venv/bin/activate

# 手动执行同步
python main.py sync -r hour    # 小时同步
python main.py sync -r day     # 日同步
python main.py sync -r week    # 周同步
python main.py sync -r month   # 月同步

# 查看状态
python main.py status

# 重置进度（谨慎使用）
python main.py reset
```

### 查看日志

```bash
# 查看不同类型的日志
tail -f /opt/zsxq-tool/logs/hourly.log   # 小时同步日志
tail -f /opt/zsxq-tool/logs/daily.log    # 日同步日志
tail -f /opt/zsxq-tool/logs/weekly.log   # 周同步日志
tail -f /opt/zsxq-tool/logs/monthly.log  # 月同步日志
tail -f /opt/zsxq-tool/logs/health.log   # 健康检查日志
tail -f /opt/zsxq-tool/logs/backup.log   # 备份日志

# 查看所有日志
ls -la /opt/zsxq-tool/logs/
```

## 🔄 更新现有部署

如果服务器上已有旧版本的定时任务，需要先清理：

```bash
# 1. 移除旧的定时任务
sudo -u zsxq crontab -r

# 2. 重新运行部署脚本
cd ~/zsxq-tool
sudo ./scripts/deploy.sh

# 3. 或者手动使用setup_cron.sh
cd /opt/zsxq-tool
sudo -u zsxq ./scripts/setup_cron.sh remove
sudo -u zsxq ./scripts/setup_cron.sh install
```

## 🏥 健康检查

```bash
# 运行健康检查
sudo -u zsxq /opt/zsxq-tool/venv/bin/python /opt/zsxq-tool/scripts/health_check.py

# 查看系统状态
sudo -u zsxq /opt/zsxq-tool/status.sh

# 测试同步功能
sudo -u zsxq /opt/zsxq-tool/venv/bin/python /opt/zsxq-tool/main.py sync -r hour --skip-upload
```

## 🚨 故障排除

### 常见问题

1. **权限问题**：
   ```bash
   sudo chown -R zsxq:zsxq /opt/zsxq-tool
   sudo chmod +x /opt/zsxq-tool/scripts/*.sh
   ```

2. **Python依赖问题**：
   ```bash
   cd /opt/zsxq-tool
   source venv/bin/activate
   pip install -r requirements.txt
   ```

3. **Cookie过期**：
   ```bash
   sudo -u zsxq vim /opt/zsxq-tool/.env
   # 更新ZSXQ_COOKIE值
   ```

4. **查看错误日志**：
   ```bash
   grep -i error /opt/zsxq-tool/logs/*.log
   ```

## 📊 监控建议

1. **设置告警**：监控日志文件中的ERROR关键字
2. **磁盘空间**：定期检查 `/opt/zsxq-tool/data/` 和 `/opt/zsxq-tool/logs/` 目录大小
3. **数据新鲜度**：确保数据文件定期更新
4. **API状态**：监控知识星球API的响应状态

部署完成后，系统将自动运行，无需手动干预！
