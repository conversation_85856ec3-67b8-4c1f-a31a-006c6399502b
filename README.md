# 知识星球数据同步工具

一个功能完整的知识星球数据处理系统，支持数据爬取、智能清洗、自动上传和定时同步。

## ✨ 核心特性

### 🚀 一键同步
- **完整流程**：下载 → 清洗 → 上传，一条命令搞定
- **智能跳过**：支持跳过任意步骤，灵活控制处理流程
- **时间范围**：支持小时、日、周、月等多种时间范围

### 🧹 数据清洗
- **内容清理**：自动清理HTML标签、特殊字符、多余空白
- **HTML转换**：将换行符转换为`<br/>`标签，段落包装为`<p>`标签
- **信息提取**：自动提取标题、标签、链接、用户提及
- **格式统一**：标准化内容格式，提升数据质量

### 🔄 智能缓存
- **用户缓存**：避免重复爬取用户信息，处理速度提升90%+
- **断点续传**：支持处理进度保存，意外中断后可继续
- **状态管理**：智能跟踪处理状态，避免重复处理

### ⏰ 定时同步
- **多级调度**：支持小时、日、周、月等多种定时任务
- **自动部署**：一键部署到服务器，自动配置定时任务
- **健康监控**：内置健康检查和日志监控

### 🏗️ 模块化架构
- **解耦设计**：爬取、清洗、上传完全分离，可独立运行
- **工作流引擎**：标准化的工作流处理，易于扩展
- **配置管理**：统一的配置管理，支持环境变量

## 📁 项目结构

```
├── main.py                   # 🚀 主程序入口
├── config.py                 # ⚙️ 配置管理
├── requirements.txt          # 📦 依赖包
├── .env.example             # 🔧 环境变量示例
│
├── models/                  # 📊 数据模型
│   ├── __init__.py
│   └── data_models.py       # 用户、帖子、星球等数据模型
│
├── services/                # 🔧 核心服务
│   ├── __init__.py
│   ├── crawler.py           # 🕷️ 知识星球API爬虫
│   ├── member_crawler.py    # 👥 成员信息爬虫
│   ├── content_cleaner.py   # 🧹 内容清洗服务
│   ├── data_processor.py    # 🔄 数据处理服务
│   ├── storage_service.py   # 💾 Supabase存储服务
│   ├── state_manager.py     # 📋 处理状态管理
│   └── user_cache_service.py # 🗄️ 用户缓存服务
│
├── workflows/               # 🔄 工作流引擎
│   ├── __init__.py
│   ├── download_workflow.py # ⬇️ 数据下载工作流
│   ├── clean_workflow.py    # 🧹 数据清洗工作流
│   ├── upload_workflow.py   # ⬆️ 数据上传工作流
│   └── user_workflow.py     # 👤 用户处理工作流
│
├── scripts/                 # 🛠️ 部署和管理脚本
│   ├── setup_cron.sh        # ⏰ 定时任务配置
│   ├── deploy.sh            # 🚀 服务器部署脚本
│   ├── manual_sync.sh       # 🔧 手动同步脚本
│   ├── backup.sh            # 💾 数据备份脚本
│   ├── scheduled_sync.py    # 📅 定时同步脚本
│   ├── health_check.py      # 🏥 健康检查脚本
│   └── monitor.py           # 📊 监控脚本
│
├── utils/                   # 🛠️ 工具模块
│   ├── __init__.py
│   └── logger.py            # 📝 日志工具
│
└── data/                    # 📂 数据目录（自动创建）
    ├── zsxq_topics_*.jsonl  # 原始数据文件
    ├── *_cleaned_*.jsonl    # 清洗后数据文件
    └── state/               # 处理状态文件
```

## 🚀 快速开始

### 1. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd zsxq-sync-tool

# 安装Python依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
vim .env
```

配置内容：
```env
# 知识星球配置
ZSXQ_COOKIE=your_zsxq_access_token_here
ZSXQ_GROUP_ID=48415154841458

# Supabase 配置
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_KEY=your-supabase-anon-key

# 可选配置
DATA_DIR=./data                    # 数据存储目录
LOG_LEVEL=INFO                     # 日志级别

# 性能优化配置
BATCH_USER_PROCESSING=true         # 启用批量用户处理（推荐）
```

### 3. 快速测试

```bash
# 测试配置是否正确
python main.py status

# 只下载最近1小时的数据（测试用）
python main.py sync -r hour --skip-upload

# 完整同步最近1小时的数据
python main.py sync -r hour
```

## 📖 使用指南

### 🎯 一键同步（推荐）

最简单的使用方式，包含完整的下载→清洗→上传流程：

```bash
# 同步最近1小时的数据（默认）
python main.py sync

# 同步不同时间范围的数据
python main.py sync -r hour     # 最近1小时
python main.py sync -r day      # 最近1天
python main.py sync -r week     # 最近1周
python main.py sync -r month    # 最近1个月

# 指定基准日期
python main.py sync -r week --before-date 2025-06-15
```

### 🔧 流程控制

可以跳过任意步骤，灵活控制处理流程：

```bash
# 只下载，不清洗和上传
python main.py sync --skip-clean --skip-upload

# 只清洗和上传，不下载新数据
python main.py sync --skip-download

# 跳过数据清洗
python main.py sync --skip-clean

# 跳过用户处理（提高速度）
python main.py sync --skip-users

# 跳过图片处理
python main.py sync --skip-images

# 不转换为HTML格式
python main.py sync --no-html
```

### 📥 分步操作

如果需要分步执行，可以使用跳过参数：

```bash
# 只下载，不清洗和上传
python main.py sync -r hour --skip-clean --skip-upload

# 只清洗和上传，使用已有数据
python main.py sync -r hour --skip-download

# 只上传，跳过下载和清洗
python main.py sync -r hour --skip-download --skip-clean
```

### 🎯 高级控制

```bash
# 跳过用户处理（提高速度）
python main.py sync -r hour --skip-users

# 跳过星球信息处理
python main.py sync -r day --skip-group

# 跳过图片处理和上传
python main.py sync -r week --skip-images

# 不转换为HTML格式
python main.py sync -r day --no-html

# 指定输出文件名
python main.py sync -r hour -o custom_data.jsonl
```

### 📊 状态管理

```bash
# 查看整体状态
python main.py status

# 重置所有进度
python main.py reset
```

## ⏰ 定时同步

### 🚀 自动部署（推荐）

使用自动部署脚本，一键配置定时任务：

```bash
# 安装定时任务
./scripts/setup_cron.sh install

# 查看已安装的任务
./scripts/setup_cron.sh show

# 测试同步脚本
./scripts/setup_cron.sh test

# 查看任务状态
./scripts/setup_cron.sh status

# 移除定时任务
./scripts/setup_cron.sh remove
```

### 📅 默认定时任务

安装后会自动配置以下定时任务：

```bash
# 每小时同步（过去1小时的新内容）- 包含数据清洗
0 * * * * python main.py sync -r hour --skip-users --skip-group

# 每天凌晨2点同步（今天所有内容）- 包含数据清洗
0 2 * * * python main.py sync -r day --skip-group

# 每周日凌晨3点同步（过去一周所有内容）- 包含数据清洗
0 3 * * 0 python main.py sync -r week

# 每月1号凌晨4点同步（过去一个月所有内容）- 包含数据清洗
0 4 1 * * python main.py sync -r month
```

### 🔧 手动同步

使用交互式脚本进行手动同步：

```bash
# 交互模式
./scripts/manual_sync.sh

# 命令行模式
./scripts/manual_sync.sh hourly     # 小时同步
./scripts/manual_sync.sh daily      # 日同步
./scripts/manual_sync.sh weekly     # 周同步
./scripts/manual_sync.sh monthly    # 月同步
./scripts/manual_sync.sh full       # 全量同步

# 只上传，不下载
./scripts/manual_sync.sh daily --upload-only
```

### 📊 监控和维护

```bash
# 健康检查
./scripts/manual_sync.sh health

# 查看状态
./scripts/manual_sync.sh status

# 查看日志
./scripts/manual_sync.sh logs

# 数据备份
./scripts/backup.sh
```

## 🖥️ 服务器部署

### 🚀 一键部署

使用自动部署脚本部署到服务器：

```bash
# 部署到服务器（需要sudo权限）
./scripts/deploy.sh

# 指定部署用户和目录
./scripts/deploy.sh --user zsxq --dir /opt/zsxq-tool
```

部署脚本会自动：
- 创建专用用户和目录
- 安装Python虚拟环境和依赖
- 配置环境变量
- 设置定时任务
- 配置日志轮转
- 创建备份策略

### 🔧 手动部署

如果需要手动部署：

```bash
# 1. 创建部署目录
sudo mkdir -p /opt/zsxq-tool
sudo chown $USER:$USER /opt/zsxq-tool

# 2. 复制项目文件
cp -r . /opt/zsxq-tool/

# 3. 创建虚拟环境
cd /opt/zsxq-tool
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 4. 配置环境变量
cp .env.example .env
vim .env

# 5. 设置定时任务
./scripts/setup_cron.sh install
```

### 🐳 Docker部署

```bash
# 构建镜像
docker build -t zsxq-sync-tool .

# 运行容器
docker run -d \
  --name zsxq-sync \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/.env:/app/.env \
  zsxq-sync-tool

# 查看日志
docker logs -f zsxq-sync
```

## 🔧 高级功能

### 📊 数据清洗详解

数据清洗是本工具的核心功能之一，能够将原始的知识星球数据转换为干净、结构化的格式：

**🧹 清洗功能**：
- **文本标准化**：处理各种换行符、空白字符
- **HTML转换**：将内容转换为标准HTML格式
- **信息提取**：自动提取标题、标签、链接、用户提及
- **格式统一**：标准化数据结构，提升数据质量

**🏷️ HTML转换规则**：
- 段落包装：`<p>内容</p>`
- 换行转换：`\n` → `<br/>`
- 粗体文本：`<e type="text_bold">` → `<strong>`
- 链接转换：`<e type="web_url">` → `<a href="..." target="_blank">`
- 标签转换：`<e type="hashtag">` → `<span class="hashtag">`
- 用户提及：`<e type="mention">` → `<span class="mention">`

**📋 清洗后数据结构**：
```json
{
  "topic_id": 123456,
  "type": "talk",
  "talk": {
    "text": "清洗后的纯文本内容",
    "html_text": "<p>HTML格式的内容</p>"
  },
  "title": "提取的标题",
  "tags": ["标签1", "标签2"],
  "links": ["链接1", "链接2"],
  "mentions": ["@用户1", "@用户2"],
  "raw_text": "原始文本内容"
}
```

### 🗄️ 智能缓存系统

**用户缓存**：
- 自动缓存已处理的用户信息
- 避免重复API调用，提升处理速度90%+
- 支持缓存失效和更新机制

**批量用户处理**：
- 新用户不立即处理，而是收集到待处理列表
- 在所有帖子处理完成后，批量处理新用户
- 大幅减少数据库查询次数和API调用
- 可通过 `BATCH_USER_PROCESSING=false` 禁用

**状态管理**：
- 跟踪处理进度，支持断点续传
- 智能跳过已处理的数据
- 异常恢复机制

### 📈 性能优化

**处理策略**：
- **小时同步**：跳过用户处理，只处理新帖子
- **日同步**：跳过星球处理，处理用户和帖子
- **周/月同步**：完整处理所有数据

**批量处理**：
- 支持大批量数据处理
- 内存优化，避免OOM
- 进度显示和错误恢复

## 📚 命令参考

### 🔍 获取帮助

```bash
# 查看主命令帮助
python main.py --help

# 查看同步命令帮助
python main.py sync --help

# 查看状态命令帮助
python main.py status --help

# 查看重置命令帮助
python main.py reset --help
```

### 📋 主要命令参数

**sync 命令（主要命令）**：
```
-r, --range {hour,day,week,month,year}  时间范围（默认：hour）
-b, --before-date YYYY-MM-DD           基准日期
-o, --output OUTPUT                    输出文件名

# 流程控制
--skip-download                        跳过下载
--skip-clean                           跳过清洗
--skip-upload                          跳过上传
--skip-users                           跳过用户处理
--skip-group                           跳过星球处理
--skip-images                          跳过图片处理

# 格式控制
--no-html                              不转换HTML（默认转换）
```

**status 命令**：
```
# 查看当前处理状态和进度
python main.py status
```

**reset 命令**：
```
# 重置所有处理进度（谨慎使用）
python main.py reset
```

## 🏗️ 系统架构

### 📦 核心组件

**🔧 服务层 (services/)**：
- `ZsxqCrawler`: 知识星球API爬虫，支持灵活时间范围
- `ContentCleaner`: 内容清洗和HTML转换
- `DataProcessor`: 数据解析和转换
- `StorageService`: Supabase存储服务
- `StateManager`: 处理进度管理
- `UserCacheService`: 用户缓存服务

**🔄 工作流层 (workflows/)**：
- `DownloadWorkflow`: 下载工作流
- `CleanWorkflow`: 清洗工作流
- `UploadWorkflow`: 上传工作流
- `UserWorkflow`: 用户处理工作流

**📊 数据层 (models/)**：
- 标准化的数据模型
- 类型安全的数据传递
- 清晰的数据契约

### 🔄 数据流程

```mermaid
graph LR
    A[知识星球API] --> B[数据爬取]
    B --> C[内容清洗]
    C --> D[HTML转换]
    D --> E[本地存储]
    E --> F[数据解析]
    F --> G[用户处理]
    G --> H[图片上传]
    H --> I[数据库存储]
```

**完整流程**：
1. **爬取阶段**：从知识星球API获取原始数据
2. **清洗阶段**：清理内容、提取信息、转换HTML
3. **存储阶段**：保存为JSONL格式文件
4. **解析阶段**：解析数据结构、处理关联关系
5. **用户阶段**：处理用户信息、获取缺失数据
6. **上传阶段**：上传图片、存储到数据库

### ⚡ 性能特性

**智能缓存**：
- 用户信息缓存，避免重复API调用
- 批量用户处理，大幅减少数据库查询
- 处理状态缓存，支持断点续传
- 缓存命中率可达100%

**批量处理**：
- 支持大批量数据处理
- 内存优化，避免OOM
- 进度显示和错误恢复

**灵活调度**：
- 多级定时任务（小时/日/周/月）
- 智能跳过策略，提升效率
- 自动错误恢复和重试
## 🚨 注意事项

### ⚠️ 重要提醒

1. **Cookie时效性**：知识星球Cookie有时效性，需要定期更新
2. **API限制**：避免过于频繁的请求，建议使用预设的时间间隔
3. **数据完整性**：定期备份处理状态文件和重要数据
4. **资源监控**：生产环境需要监控磁盘空间和内存使用

### 🔧 故障排除

**常见问题**：
- **Cookie过期**：更新`.env`文件中的`ZSXQ_COOKIE`
- **网络超时**：调整`ZSXQ_REQUEST_TIMEOUT`和重试参数
- **存储空间不足**：检查Supabase存储配额
- **数据库连接失败**：验证Supabase配置信息

**日志查看**：
```bash
# 查看详细日志
export LOG_LEVEL=DEBUG
python main.py sync -r hour

# 查看系统日志
journalctl -u zsxq-sync -f
```

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

---

**🎉 现在你已经拥有了一个功能完整的知识星球数据同步系统！**

- 🚀 **一键同步**：`python main.py sync`
- ⏰ **定时任务**：`./scripts/setup_cron.sh install`
- 🖥️ **服务器部署**：`./scripts/deploy.sh`
- 📊 **数据清洗**：自动HTML转换和内容优化
- 🗄️ **智能缓存**：90%+性能提升

开始你的数据同步之旅吧！ 🎯
